{"name": "root", "version": "0.1.0", "description": "scmally library", "private": true, "engines": {"node": ">=14"}, "author": "李兆盖", "license": "UNLICENSED", "scripts": {"postinstall": "husky install && yarn run build", "prerelease": "yarn run build:full && yarn run lint", "release": "yarn prerelease &&cross-env CI=1 lerna version && lerna publish from-git --yes", "lint": "yarn run eslint && yarn prettier:fix", "lint:fix": "yarn run eslint:fix && yarn prettier:fix", "eslint": "nb-eslint --report-unused-disable-directives --cache .", "eslint:fix": "yarn run eslint --fix", "prettier:cli": "nb-prettier \"**/*.ts\" \"**/*.js\" \"**/*.md\"", "prettier:check": "yarn run prettier:cli --check", "prettier:fix": "yarn run prettier:cli --write", "clean": "lerna run clean && del 'packages/*/dist'", "clean:lerna": "lerna clean", "build": "lerna run build --sort", "build:full": "yarn install && yarn run clean && yarn run build", "pretest": "yarn run clean && yarn run build", "test": "yarn run pretest && yarn run jest && yarn run posttest", "posttest": "yarn run lint", "test:ci": "node --experimental-vm-modules node_modules/jest/bin/jest.js --collectCoverage --coverageDirectory=\"./coverage\" --reporters=default --reporters=jest-junit --watchAll=false", "jest": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "start": "yarn workspace @scmally/server run start", "start:server": "yarn workspace @scmally/server run start:server", "start:worker:job": "yarn workspace @scmally/server run start:worker:job", "start:worker:export": "yarn workspace @scmally/server run start:worker:export", "start:worker": "yarn workspace @scmally/server run start:worker", "start:test": "yarn workspace @scmally/server run start:test", "dev": "yarn workspace @scmally/server run dev", "gen": "yarn workspace @scmally/server run migration:generate", "genrun": "yarn workspace @scmally/server run migration:run", "rvt": "yarn workspace @scmally/server run migration:revert", "ngcc": "ngcc", "build:admin": "yarn build:full && yarn workspace @scmally/server run compile:admin && yarn workspace @scmally/server run build:admin", "docker:build": "yarn build:full && yarn workspace @scmally/server run compile:admin && yarn workspace @scmally/server run build:admin && docker build -t scmally ."}, "devDependencies": {"@commitlint/cli": "^17.4.2", "@commitlint/config-conventional": "^17.4.2", "@commitlint/travis-cli": "^17.4.2", "@graphql-codegen/typescript-graphql-request": "^4.5.9", "@nutol/build": "^0.2.13", "@nutol/eslint-config": "^0.2.8", "@nutol/monorepo": "^0.2.10", "@types/jest": "^29.4.0", "@types/node": "^18.13.0", "@types/superagent": "^4.1.18", "@typescript-eslint/eslint-plugin": "^5.51.0", "@typescript-eslint/parser": "^5.51.0", "@vendure/core": "^2.0.0", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "del-cli": "^5.0.0", "eslint": "^8.34.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-eslint-plugin": "^5.0.8", "eslint-plugin-jest": "^27.2.1", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "if-env": "^1.0.4", "jest": "^29.4.2", "jest-junit": "^15.0.0", "lerna": "^6.4.1", "lint-staged": "^13.1.1", "prettier": "^2.8.4", "ts-jest": "^29.0.5", "typescript": "5.0.4"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "if-env LINT_STAGED=0 && echo \"lint-staged disabled via LINT_STAGED env var\" || lint-staged"}}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "workspaces": ["packages/*", "apps/*"], "packageManager": "yarn@3.4.1", "dependencies": {"@aws-sdk/client-s3": "^3.383.0", "@aws-sdk/lib-storage": "^3.386.0", "@types/lodash": "^4.14.194", "@types/qrcode": "^1.5.5", "@vendure/migrate-v2": "^0.4.0", "number-precision": "^1.6.0", "qrcode": "^1.5.3", "redis": "^3.1.2", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "xlsx": "^0.18.5", "youzanyun-sdk": "^1.0.3"}}