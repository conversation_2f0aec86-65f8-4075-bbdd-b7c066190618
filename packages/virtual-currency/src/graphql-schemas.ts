import {gql} from 'graphql-tag';
// eslint-disable-next-line
const _scalar = gql`
  scalar DateTime
`;

const sharedTypes = gql`
  enum SymbolType {
    in
    out
    frz
  }

  enum VirtualCurrencyCode {
    ## 购物金
    shoppingCredits
  }
  type VirtualCurrencyAccount implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    customer: Customer
    customerId: ID
    virtualCurrencyCode: VirtualCurrencyCode
    amount: Int
  }

  enum VirtualCurrencySourceType {
    # 订单
    order
    # 售后虚拟币回收（平台收回用户虚拟币）
    reclaimed
    # 主动退款回收虚拟币
    merchantVoluntaryReclaimed
    # 外部系统
    externalSystem
    # 后台操作
    adminOperation
    # 订单抵扣
    orderDeduction
    # 售后虚拟币退回（平台退还用户虚拟币）
    returned
    # 主动退款退回虚拟币
    merchantVoluntaryRefundReturn
    # 虚拟币过期
    expired
  }

  type VirtualCurrencyHistory implements Node {
    id: ID!
    createdAt: DateTime
    updatedAt: DateTime
    customer: Customer
    customerId: ID
    virtualCurrencyAccount: VirtualCurrencyAccount
    virtualCurrencyAccountId: ID
    amount: Int
    symbolType: SymbolType
    beforeAmount: Int
    afterAmount: Int
    remark: String
    sourceType: VirtualCurrencySourceType
    sourceValue: String
    channelId: ID
  }

  type VirtualCurrencyHistoryList implements PaginatedList {
    items: [VirtualCurrencyHistory!]!
    totalItems: Int!
  }

  input VirtualCurrencyHistoryListOptions
`;

export const shopSchemaExtensions = gql`
  ${sharedTypes}
  extend type Query {
    getVirtualCurrencyBalance(virtualCurrencyCode: VirtualCurrencyCode!): Int
    getVirtualCurrencyHistory(
      virtualCurrencyCode: VirtualCurrencyCode!
      options: VirtualCurrencyHistoryListOptions
    ): VirtualCurrencyHistoryList
  }
`;

export const adminSchemaExtensions = gql`
  ${sharedTypes}
  extend type Query {
    getVirtualCurrencyBalance(virtualCurrencyCode: VirtualCurrencyCode!, customerId: ID!): Int
    getVirtualCurrencyHistory(
      virtualCurrencyCode: VirtualCurrencyCode!
      options: VirtualCurrencyHistoryListOptions
      customerId: ID!
    ): VirtualCurrencyHistoryList
  }
  extend type Mutation {
    updateVirtualCurrencyBalance(
      customerId: ID!
      virtualCurrencyCode: VirtualCurrencyCode!
      symbolType: SymbolType!
      amount: Int!
      remark: String
    ): Int
  }
`;
