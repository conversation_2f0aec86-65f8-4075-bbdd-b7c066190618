import {Args, Mutation, Parent, Query, ResolveField, Resolver} from '@nestjs/graphql';
import {Allow, Ctx, ID, ListQueryOptions, RelationPaths, Relations, RequestContext, Transaction} from '@vendure/core';
import {Coupon, UserCoupon} from '../entities';
import {ComplimentaryCouponObjectInput, CouponInput} from '../generated-admin-types';
import {CouponType} from '../generated-shop-types';
import {CouponFailureOperate, CouponOperate, CouponPermission, UserCouponPermission} from '../permission-definition';
import {CouponService, CrontabService} from '../service';
@Resolver('Coupon')
export class CouponAdminResolver {
  constructor(private couponService: CouponService, private crontabService: CrontabService) {}

  @ResolveField()
  receivedNumber(@Ctx() ctx: RequestContext, @Parent() coupon: Coupon) {
    return this.couponService.receivedNumber(ctx, coupon.id);
  }

  @ResolveField()
  async residualNumber(@Ctx() ctx: RequestContext, @Parent() coupon: Coupon) {
    const receivedNumber = await this.receivedNumber(ctx, coupon);
    return coupon.totalQuantity - receivedNumber;
  }

  @ResolveField()
  haveBeenUsedNumber(@Ctx() ctx: RequestContext, @Parent() coupon: Coupon) {
    return this.couponService.haveBeenUsedNumber(ctx, coupon.id);
  }

  @ResolveField()
  totalOrderAmount(@Ctx() ctx: RequestContext, @Parent() coupon: Coupon) {
    return this.couponService.totalOrderAmount(ctx, coupon.id);
  }

  @ResolveField()
  async customerUnitPrice(@Ctx() ctx: RequestContext, @Parent() coupon: Coupon) {
    const totalOrderAmount = await this.totalOrderAmount(ctx, coupon);
    const haveBeenUsedNumber = await this.haveBeenUsedNumber(ctx, coupon);
    return haveBeenUsedNumber ? totalOrderAmount / haveBeenUsedNumber : 0;
  }

  @Query()
  @Allow(CouponPermission.Read)
  coupons(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<Coupon>,
    @Relations({entity: Coupon})
    relations: RelationPaths<Coupon>,
  ) {
    return this.couponService.findAll(ctx, options, relations, true);
  }

  @Query()
  @Allow(CouponPermission.Read)
  coupon(
    @Ctx() ctx: RequestContext,
    @Args('couponId') couponId: ID,
    @Args('options') options: ListQueryOptions<Coupon>,
    @Relations({entity: Coupon})
    relations: RelationPaths<Coupon>,
  ) {
    return this.couponService.findOne(ctx, couponId, options, relations);
  }

  @Query()
  @Allow(UserCouponPermission.Read)
  userCoupons(
    @Ctx() ctx: RequestContext,
    @Args('options') options: ListQueryOptions<UserCoupon>,
    @Args('customerPhone') customerPhone: string,
    @Args('couponName') couponName: string,
    @Args('couponType') couponType: CouponType,
    @Args('customerId') customerId: ID,
    @Relations({entity: UserCoupon})
    relations: RelationPaths<UserCoupon>,
  ) {
    return this.couponService.getUserCouponAll(
      ctx,
      options,
      relations,
      true,
      customerId,
      customerPhone,
      couponName,
      couponType,
    );
  }

  @Query()
  @Allow(UserCouponPermission.Read)
  userCoupon(
    @Ctx() ctx: RequestContext,
    @Args('userCouponId') userCouponId: ID,
    @Args('options') options: ListQueryOptions<UserCoupon>,
    @Relations({entity: UserCoupon})
    relations: RelationPaths<UserCoupon>,
  ) {
    return this.couponService.getUserCouponOne(ctx, userCouponId, options, relations, true);
  }

  @Query()
  @Allow(CouponPermission.Read)
  couponHolder(
    @Ctx() ctx: RequestContext,
    @Args('couponId') couponId: ID,
    @Args('options') options: ListQueryOptions<UserCoupon>,
    @Relations({entity: UserCoupon})
    relations: RelationPaths<UserCoupon>,
  ) {
    return this.couponService.couponHolder(ctx, couponId, options, relations);
  }

  @Transaction()
  @Mutation()
  @Allow(CouponPermission.Create, CouponPermission.Update)
  upsertCoupon(@Ctx() ctx: RequestContext, @Args('input') input: CouponInput) {
    return this.couponService.upsertCoupon(ctx, input);
  }

  @Transaction()
  @Mutation()
  @Allow(CouponFailureOperate.Permission)
  failureCoupon(@Ctx() ctx: RequestContext, @Args('couponId') couponId: ID) {
    return this.couponService.failure(ctx, couponId);
  }

  @Transaction()
  @Mutation()
  @Allow(CouponPermission.Delete)
  softDeleteCoupon(@Ctx() ctx: RequestContext, @Args('couponId') couponId: ID) {
    return this.couponService.softDeleteCoupon(ctx, couponId);
  }

  @Transaction()
  @Mutation()
  @Allow(CouponFailureOperate.Permission)
  enableSwitchCoupon(@Ctx() ctx: RequestContext, @Args('couponId') couponId: ID) {
    return this.couponService.enableSwitch(ctx, couponId);
  }

  @Transaction()
  @Query()
  @Allow(CouponPermission.Read)
  couponDataStatistic(@Ctx() ctx: RequestContext, @Args('couponId') couponId: ID) {
    return this.couponService.couponDataStatistic(ctx, couponId);
  }

  @Transaction()
  @Mutation()
  @Allow(UserCouponPermission.Update)
  failureUserCoupon(@Ctx() ctx: RequestContext, @Args('userCouponId') userCouponId: ID) {
    return this.couponService.failureUserCoupon(ctx, userCouponId);
  }

  @Transaction()
  @Mutation()
  @Allow(CouponOperate.Permission)
  complimentaryCouponToMember(@Ctx() ctx: RequestContext, @Args('input') input: ComplimentaryCouponObjectInput) {
    return '当前接口已废弃';
    // return this.couponService.complimentaryCouponToMember(ctx, input);
  }

  @Transaction()
  @Mutation()
  @Allow(UserCouponPermission.Create)
  grantCoupon(
    @Ctx() ctx: RequestContext,
    @Args('quantity') quantity: number,
    @Args('couponId') couponId: ID,
    @Args('customerId') customerId: ID,
  ) {
    return this.couponService.grantCoupon(ctx, quantity, couponId, customerId);
  }

  // @Transaction()
  // @Mutation()
  // @Allow(CouponOperate.Permission)
  // complimentaryCouponToMember(@Ctx() ctx: RequestContext, @Args('input') input: ComplimentaryCouponObjectInput) {
  //   return this.couponService.couponGrant(ctx, input);
  // }

  // 获取用户可领取的优惠券列表
  @Query()
  @Allow(CouponPermission.Read, UserCouponPermission.Read)
  getAvailableCouponList(
    @Ctx() ctx: RequestContext,
    @Args('customerId') customerId: ID,
    @Args('couponName') couponName: string,
    @Args('options') options: ListQueryOptions<Coupon>,
    @Relations({entity: Coupon})
    relations: RelationPaths<Coupon>,
  ) {
    return this.couponService.getAvailableCouponList(ctx, customerId, couponName, options, relations);
  }

  // 手动执行优惠券到期提醒
  @Mutation()
  @Allow(CouponPermission.Permission)
  manualSendCouponExpiredNotice(@Ctx() ctx: RequestContext) {
    return this.crontabService.manualSendCouponExpiredNotice();
  }
}
