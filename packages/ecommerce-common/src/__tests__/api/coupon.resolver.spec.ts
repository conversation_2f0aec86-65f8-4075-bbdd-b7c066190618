import {
  createTestEnvironment,
  registerInitializer,
  SimpleGraphQLClient,
  SqljsInitializer,
  testConfig,
} from '@vendure/testing';

import {initialData} from '../fixtures/initial-data';
import {
  ChannelService,
  DefaultLogger,
  EntityIdStrategy,
  LogLevel,
  mergeConfig,
  RequestContextService,
} from '@vendure/core';
import {TestServer} from '@vendure/testing/lib/test-server';
import {CommonPlugin} from '../../ecommerce-common.plugin';
import {COUPONS, FAILURE, UPSERT_COUPON} from '../graphql/graphql-test';
import {
  ApplicableType,
  CouponState,
  CouponType,
  PreferentialType,
  ValidityPeriodType,
} from '../../generated-shop-types';
import {CREATE_CUSTOMER} from '../graphql/shop-api.graphql';
require('dotenv').config();
jest.setTimeout(20000);

export class MyEntityIdStrategy implements EntityIdStrategy<'increment'> {
  readonly primaryKeyType = 'increment';

  decodeId(id: string): number {
    const asNumber = parseInt(id, 10);
    return Number.isNaN(asNumber) ? -1 : asNumber;
  }

  encodeId(primaryKey: number): string {
    return primaryKey.toString();
  }
}

describe('Coupon Plugin', function () {
  let server: TestServer;
  let adminClient: SimpleGraphQLClient;
  let shopClient: SimpleGraphQLClient;
  let serverStarted = false;

  beforeAll(async () => {
    registerInitializer('sqljs', new SqljsInitializer('__data__'));
    testConfig.apiOptions.port = 4002;
    const config = mergeConfig(testConfig, {
      logger: new DefaultLogger({level: LogLevel.Debug}),
      dbConnectionOptions: {
        synchronize: true,
      },
      entityOptions: {entityIdStrategy: new MyEntityIdStrategy()},
      plugins: [CommonPlugin],
    });
    ({server, adminClient, shopClient} = createTestEnvironment(config));
    await server.init({
      initialData: {
        ...initialData,
      },
      productsCsvPath: `${__dirname}/../fixtures/products.csv`,
    });
    await adminClient.asSuperAdmin();
    await adminClient.query(CREATE_CUSTOMER, {
      input: {
        emailAddress: '<EMAIL>',
        firstName: 'New',
        lastName: 'Customer',
      },
      password: 'Mytest0012..',
    });
    await shopClient.asUserWithCredentials('<EMAIL>', 'Mytest0012..');
    const ctx = await server.app.get(RequestContextService).create({
      apiType: 'admin',
    });
    const channelService = server.app.get(ChannelService);
    await channelService.update(ctx, {
      id: 1,
      defaultTaxZoneId: '1',
      defaultShippingZoneId: '1',
    });
    serverStarted = true;
  }, 60000);

  it('Should start successfully', async () => {
    expect(serverStarted).toBe(true);
  });
  it('create coupon success', async () => {
    const {createCoupon} = await adminClient.query(UPSERT_COUPON, {
      input: {
        name: 'test',
        remarks: 'test',
        type: CouponType.Discount,
        preferentialContent: {
          preferentialType: PreferentialType.Satisfy,
          minimum: 10,
          discount: 10,
          maximumOffer: 10,
        },
        validityPeriod: {
          type: ValidityPeriodType.ValidDays,
          numberOfDays: 90,
        },
        totalQuantity: 10,
        applicableProduct: {
          applicableType: ApplicableType.All,
        },
        claimRestriction: 10,
        whetherRestrictions: 10,
        whetherRestrictUsers: false,
        introduce: 'test',
      },
    });
    expect(createCoupon.name).toBe('test');
  });
  it('create coupon fail', async () => {
    const {createCoupon} = await adminClient.query(UPSERT_COUPON, {
      input: {
        name: 'test',
        remarks: 'test',
        type: CouponType.Discount,
        preferentialContent: {
          preferentialType: PreferentialType.Satisfy,
          minimum: 10,
          discount: 10,
          maximumOffer: 10,
        },
        validityPeriod: {
          type: ValidityPeriodType.ValidDays,
        },
        totalQuantity: 10,
        applicableProduct: {
          applicableType: ApplicableType.All,
        },
        claimRestriction: 10,
        whetherRestrictions: 10,
        whetherRestrictUsers: false,
        introduce: 'test',
      },
    });
    expect(createCoupon.name).toBe('test');
  });
  it('update coupon success', async () => {
    const {updateCoupon} = await adminClient.query(UPSERT_COUPON, {
      input: {
        id: '1',
        name: 'test',
        remarks: 'test',
        type: CouponType.Discount,
        preferentialContent: {
          preferentialType: PreferentialType.Satisfy,
          minimum: 10,
          discount: 10,
          maximumOffer: 10,
        },
        validityPeriod: {
          type: ValidityPeriodType.ValidDays,
          numberOfDays: 90,
        },
        totalQuantity: 10,
        applicableProduct: {
          applicableType: ApplicableType.All,
        },
        claimRestriction: 10,
        whetherRestrictions: 10,
        whetherRestrictUsers: false,
        introduce: 'test',
      },
    });
    expect(updateCoupon.name).toBe('test');
  });
  it('update coupon fail', async () => {
    const {updateCoupon} = await adminClient.query(UPSERT_COUPON, {
      input: {
        id: '1',
        name: 'test',
        remarks: 'test',
        type: CouponType.Discount,
        preferentialContent: {
          preferentialType: PreferentialType.Satisfy,
          minimum: 10,
          discount: 10,
          maximumOffer: 10,
        },
        validityPeriod: {
          type: ValidityPeriodType.ValidDays,
        },
        totalQuantity: 10,
        applicableProduct: {
          applicableType: ApplicableType.All,
        },
        claimRestriction: 10,
        whetherRestrictions: 10,
        whetherRestrictUsers: false,
        introduce: 'test',
      },
    });
    expect(updateCoupon.name).toBe('test');
  });
  it('failure to coupon', async () => {
    const {failure} = await adminClient.query(FAILURE, {
      couponId: '1',
    });
    expect(failure.state).toBe(CouponState.Failure);
  });

  it('query coupon list success', async () => {
    const {coupons} = await adminClient.query(COUPONS, {});
    expect(coupons.totalItems).toBe(0);
  });
  it('query coupon list fail', async () => {
    const {coupons} = await adminClient.query(COUPONS, {
      options: {
        filter: {
          name: 'test',
        },
      },
    });
    expect(coupons.totalItems).toBe(0);
  });
});
