import gql from 'graphql-tag';
import {CUSTOM_PAGE, HOT_WORD} from './fragments';

export const COUPON = gql`
  fragment Coupon on Coupon {
    id
    createdAt
    updatedAt
    name
    remarks
    type
    claimRestriction
    whetherRestrictUsers
    customerId
    introduce
    totalQuantity
    preferentialContent {
      preferentialType
      minimum
      discount
      maximumOffer
      includingDiscountProducts
    }
    validityPeriod {
      type
      startTime
      endTime
      numberOfDays
    }
    applicableProduct {
      applicableType
      productIds
    }
  }
`;
export const FAILURE = gql`
  mutation failure($couponId: ID!) {
    failure(couponId: $couponId) {
      ...Coupon
    }
  }
  ${COUPON}
`;

export const COUPONS = gql`
  query coupons($options: CouponListOptions!) {
    coupons(options: $options) {
      totalItems
      items {
        ...Coupon
      }
    }
  }
  ${COUPON}
`;

export const COUPON_ONE = gql`
  query coupon($options: CouponListOptions!, $couponId: ID!) {
    coupon(options: $options, couponId: $couponId) {
      ...Coupon
    }
  }
  ${COUPON}
`;

export const UPSERT_COUPON = gql`
  mutation upsertCoupon($input: CouponInput!) {
    upsertCoupon(input: $input) {
      ...Coupon
    }
  }
  ${COUPON}
`;
export const UPSERT_ANNOUNCEMENT = gql`
  mutation upsertAnnouncement($input: AnnouncementInput!) {
    upsertAnnouncement(input: $input) {
      id
      value
    }
  }
`;

export const ANNOUNCEMENT = gql`
  query announcement($options: AnnouncementListOptions, $announcementId: ID!) {
    announcement(options: $options, announcementId: $announcementId) {
      id
      value
    }
  }
`;
export const ANNOUNCEMENTS = gql`
  query announcements($options: AnnouncementListOptions) {
    announcements(options: $options) {
      items {
        id
        value
      }
      totalItems
    }
  }
`;

export const DELETE_HOT_WORD = gql`
  mutation deleteHotWord($hotWordId: ID!) {
    deleteHotWord(hotWordId: $hotWordId) {
      message
    }
  }
`;

export const GET_HOT_WORD = gql`
  query hotWord($hotWordId: ID!, $options: HotWordListOptions) {
    hotWord(options: $options, hotWordId: $hotWordId) {
      ...HotWord
    }
  }
  ${HOT_WORD}
`;
export const HOT_WORDS = gql`
  query hotWords($options: HotWordListOptions) {
    hotWords(options: $options) {
      items {
        ...HotWord
      }
      totalItems
    }
  }
  ${HOT_WORD}
`;

export const UPSERT_HOT_WORD = gql`
  mutation upsertHotWord($input: HotWordInput!) {
    upsertHotWord(input: $input) {
      ...HotWord
    }
  }
  ${HOT_WORD}
`;
export const SERIES_PRODUCTS = gql`
  query seriesProducts($collectionId: ID!, $options: ProductListOptions) {
    seriesProducts(collectionId: $collectionId, options: $options) {
      items {
        id
        name
        slug
      }
      totalItems
    }
  }
`;

export const DELETE_CUSTOM_PAGE = gql`
  mutation deleteCustomPage($customPageId: ID!) {
    deleteCustomPage(customPageId: $customPageId) {
      message
    }
  }
`;
export const TRANSITION_ENABLE_CUSTOM_PAGE = gql`
  mutation transitionEnableCustomPage($enable: Boolean!, $customPageId: ID!) {
    transitionEnableCustomPage(enable: $enable, customPageId: $customPageId) {
      ...CustomPage
    }
  }
  ${CUSTOM_PAGE}
`;

export const UPSERT_CUSTOM_PAGE = gql`
  mutation upsertCustomPage($input: CustomPageInput) {
    upsertCustomPage(input: $input) {
      ...CustomPage
    }
  }
  ${CUSTOM_PAGE}
`;

export const CUSTOM_PAGE_ONE = gql`
  query customPage($customPageId: ID) {
    customPage(customPageId: $customPageId) {
      ...CustomPage
    }
  }
  ${CUSTOM_PAGE}
`;

export const CREATE_BANNER = gql`
  mutation createBanner($input: BannerInput!) {
    createBanner(input: $input) {
      image
      redirect
      redirectType
    }
  }
`;

export const BANNERS = gql`
  query banners($options: BannerListOptions) {
    banners(options: $options) {
      items {
        id
        image
        redirect
        redirectType
      }
      totalItems
    }
  }
`;

export const BANNER = gql`
  query banner($bannerId: String!, $options: BannerListOptions) {
    banner(bannerId: $bannerId, options: $options) {
      id
      image
      redirect
      redirectType
    }
  }
`;

export const UPDATE_BANNER = gql`
  mutation updateBanner($input: BannerInput!, $bannerId: String!) {
    updateBanner(input: $input, bannerId: $bannerId) {
      id
      image
      redirect
      redirectType
    }
  }
`;

export const DELETE_BANNER = gql`
  mutation deleteBanner($bannerId: String!) {
    deleteBanner(bannerId: $bannerId)
  }
`;
