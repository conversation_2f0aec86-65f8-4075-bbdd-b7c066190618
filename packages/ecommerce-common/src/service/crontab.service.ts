import {Injectable} from '@nestjs/common';
import {<PERSON>ron} from '@nestjs/schedule';
import {Logger, ProcessContext} from '@vendure/core';
import {BlindBoxActivityService, BlindBoxOrderService} from './blind-box';
import {CommonService} from './common.service';
import {CouponBundleService} from './coupon-bundle.service';
import {CouponService} from './coupon.service';
import {CustomPageService} from './custom-page.service';
import {CustomerSummaryService} from './customer-summary.service';
import {DiscountActivityService} from './discount-activity.service';
import {DistributorService} from './distributor.service';
import {ExternalOrderService} from './external-order.service';
import {FirstCustomerBenefitService} from './first-customer-benefit.service';
import {FullDiscountPresentService} from './full-discount-present.service';
import {OperationPlanService} from './operation-plan.service';
import {PaymentRewardActivityService} from './payment-reward-activity.service';
import {PointsProductService} from './points-product.service';
import {ProductCustomService} from './product-custom.service';
import {PurchasePremiumService} from './purchase-premium.service';
import {ReviewProductService} from './review-product.service';
import {SelectiveGiftActivityService} from './selective-gift-activity.service';
import {ShoppingCreditsClaimActivityService} from './shopping-credits-claim-activity-service';
import {ShoppingCreditsConfigService} from './shopping-credits-config.service';
import {ShoppingCreditsDeductionActivityService} from './shopping-credits-deduction-activity-service';
import {WechatLogisticsCompanyService} from './wechat-logistics-company.service';
import {WishBoxService} from './wish-box.service';

@Injectable()
export class CrontabService {
  constructor(
    private reviewProductService: ReviewProductService,
    private customPageService: CustomPageService,
    private couponService: CouponService,
    private purchasePremiumService: PurchasePremiumService,
    private distributorService: DistributorService,
    private productCustomService: ProductCustomService,
    private processContext: ProcessContext,
    private fullDiscountPresentService: FullDiscountPresentService,
    private discountActivityService: DiscountActivityService,
    private commonService: CommonService,
    private operationPlanService: OperationPlanService,
    private wechatLogisticsCompanyService: WechatLogisticsCompanyService,
    private selectiveGiftActivityService: SelectiveGiftActivityService,
    private paymentRewardActivityService: PaymentRewardActivityService,
    private couponBundleService: CouponBundleService,
    private firstCustomerBenefitService: FirstCustomerBenefitService,
    private pointsProductService: PointsProductService,
    private blindBoxActivityService: BlindBoxActivityService,
    private blindBoxOrderService: BlindBoxOrderService,
    private shoppingCreditsClaimActivityService: ShoppingCreditsClaimActivityService,
    private shoppingCreditsDeductionActivityService: ShoppingCreditsDeductionActivityService,
    private shoppingCreditsConfigService: ShoppingCreditsConfigService,
    private customerSummaryService: CustomerSummaryService,
    private externalOrderService: ExternalOrderService,
    private wishBoxService: WishBoxService,
  ) {}

  private isDisableCron = false;
  init(isDisableCron: boolean) {
    this.isDisableCron = isDisableCron;
  }

  // 是否超时自动评价
  private isTimeoutAutomaticReview = false;
  /**
   * 超时自动评价
   */
  @Cron('0 0 * * *')
  async timeoutAutomaticReview() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isTimeoutAutomaticReview) {
        return;
      }
      this.isTimeoutAutomaticReview = true;
      try {
        Logger.debug(`定时器执行:超时自动评价`);
        await this.reviewProductService.timeoutAutomaticReview();
        Logger.debug(`定时器执行:超时自动评价完成`);
      } catch (error) {
        Logger.error(`定时器:超时自动评价错误:${error}`);
      } finally {
        this.isTimeoutAutomaticReview = false;
      }
    }
  }
  // 是否定时发布自定义页面
  private isTimedPublishingPage = false;
  /**
   * 定时发布自定义页面
   */
  @Cron('*/1 * * * *')
  async timedPublishingPage() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isTimedPublishingPage) {
        return;
      }
      this.isTimedPublishingPage = true;
      try {
        Logger.debug(`定时器执行:定时发布自定义页面`);
        await this.customPageService.timedPublishingPageAll();
        Logger.debug(`定时器执行:定时发布自定义页面完成`);
      } catch (error) {
        Logger.error(`定时器:定时发布自定义页面错误:${error}`);
      } finally {
        this.isTimedPublishingPage = false;
      }
    }
  }
  // 是否正在执行优惠券定时停启用
  private isTimeoutCoupon = false;
  /**
   * 优惠券定时停启用
   */
  @Cron('*/1 * * * *')
  async timeoutCoupon() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isTimeoutCoupon) {
        return;
      }
      this.isTimeoutCoupon = true;
      try {
        Logger.debug(`定时器执行:优惠券定时停启用`);
        await this.couponService.timeoutCouponAll();
        Logger.debug(`定时器执行:优惠券定时停启用完成`);
      } catch (error) {
        Logger.error(`定时器:优惠券定时停启用错误:${error}`);
      } finally {
        this.isTimeoutCoupon = false;
      }
    }
  }
  // 是否正在执行用户优惠券定时停启用
  private isTimeoutUserCoupon = false;

  /**
   * 用户优惠券定时停启用
   */
  @Cron('*/1 * * * *')
  async timeoutUserCoupon() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isTimeoutUserCoupon) {
        return;
      }
      this.isTimeoutUserCoupon = true;
      try {
        Logger.debug(`定时器执行:用户优惠券定时停启用`);
        await this.couponService.timeoutUserCouponAll();
        Logger.debug(`定时器执行:用户优惠券定时停启用完成`);
      } catch (error) {
        Logger.error(`定时器:用户优惠券定时停启用错误:${error}`);
      } finally {
        this.isTimeoutUserCoupon = false;
      }
    }
  }

  // 是否正在执行加价购定时停启用
  private isTimeoutPurchasePremium = false;
  /**
   * 加价购定时停启用
   */
  @Cron('*/1 * * * *')
  async timeoutPurchasePremium() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isTimeoutPurchasePremium) {
        return;
      }
      this.isTimeoutPurchasePremium = true;
      try {
        Logger.debug(`定时器执行:加价购定时停启用`);
        await this.purchasePremiumService.timeoutPurchasePremiumAll();
        Logger.debug(`定时器执行:加价购定时停启用完成`);
      } catch (error) {
        Logger.error(`定时器:加价购定时停启用错误:${error}`);
      } finally {
        this.isTimeoutPurchasePremium = false;
      }
    }
  }

  // 是否正在执行支付有礼活动定时停启用
  private isTimeoutPaymentRewardActivity = false;
  /**
   * 支付有礼活动定时停启用
   */
  @Cron('*/1 * * * *')
  async timeoutPaymentRewardActivity() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isTimeoutPaymentRewardActivity) {
        return;
      }
      this.isTimeoutPaymentRewardActivity = true;
      try {
        Logger.debug(`定时器执行:支付有礼活动定时停启用`);
        await this.paymentRewardActivityService.timeoutPaymentRewardActivityAll();
        Logger.debug(`定时器执行:支付有礼活动定时停启用完成`);
      } catch (error) {
        Logger.error(`定时器执行:支付有礼活动定时停启用错误:${error}`);
      } finally {
        this.isTimeoutPaymentRewardActivity = false;
      }
    }
  }

  // /**
  //  * 分销员超时解绑
  //  */
  // @Cron('*/1 * * * *')
  // async timeoutDistributor() {
  //   if (this.processContext.isWorker) {
  //     try {
  //       Logger.debug(`定时器执行:分销员超时解绑`);
  //       await this.distributorService.timeoutDistributor();
  //       Logger.debug(`定时器执行:分销员超时解绑完成`);
  //     } catch (error) {
  //       Logger.error(`定时器:分销员超时解绑错误:${error}`);
  //     }
  //   }
  // }
  // 是否正在执行产品定时上下架
  private isPutOnAndTakeOffShelvesRegularly = false;
  /**
   * 产品定时上下架
   */
  @Cron('*/1 * * * *')
  async putOnAndTakeOffShelvesRegularly() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isPutOnAndTakeOffShelvesRegularly) {
        return;
      }
      this.isPutOnAndTakeOffShelvesRegularly = true;
      try {
        Logger.debug(`定时器执行:产品定时上下架`);
        await this.productCustomService.putOnAndTakeOffShelvesRegularlyAll();
        Logger.debug(`定时器执行:产品定时上下架完成`);
      } catch (error) {
        Logger.error(`定时器:产品定时上下架错误:${error}`);
      } finally {
        this.isPutOnAndTakeOffShelvesRegularly = false;
      }
    }
  }

  // 是否正在执行用户优惠券过期提醒
  private isUserCouponExpirationReminder = false;

  /**
   * 用户优惠券过期提醒
   */
  @Cron('0 0 * * *')
  async userCouponExpirationReminder() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isUserCouponExpirationReminder) {
        return;
      }
      this.isUserCouponExpirationReminder = true;
      try {
        Logger.debug(`定时器执行:用户优惠券过期提醒`);
        await this.couponService.userCouponExpirationReminder();
        Logger.debug(`定时器执行:用户优惠券过期提醒完成`);
      } catch (error) {
        Logger.error(`定时器:用户优惠券过期提醒错误:${error}`);
      } finally {
        this.isUserCouponExpirationReminder = false;
      }
    }
  }
  // 是否正在执行满减满赠活动状态变更
  private isFullReductionAndFullGift = false;

  /**
   * 满减满赠活动状态变更
   */

  @Cron('*/1 * * * *')
  async fullReductionAndFullGift() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isFullReductionAndFullGift) {
        return;
      }
      this.isFullReductionAndFullGift = true;
      try {
        Logger.debug(`定时器执行:满减满赠活动状态变更`);
        await this.fullDiscountPresentService.fullReductionAndFullGiftAll();
        Logger.debug(`定时器执行:满减满赠活动状态变更完成`);
      } catch (error) {
        Logger.error(`定时器:满减满赠活动状态变更错误:${error}`);
      } finally {
        this.isFullReductionAndFullGift = false;
      }
    }
  }
  private isSelectiveGiftActivity = false;

  /**
   * 任选满赠活动状态变更
   */
  @Cron('*/1 * * * *')
  async selectiveGiftActivity() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isSelectiveGiftActivity) {
        return;
      }
      this.isSelectiveGiftActivity = true;
      try {
        Logger.debug(`定时器执行:任选满赠活动状态变更`);
        await this.selectiveGiftActivityService.selectiveGiftActivityAll();
        Logger.debug(`定时器执行:任选满赠活动状态变更完成`);
      } catch (error) {
        Logger.error(`定时器:任选满赠活动状态变更错误:${error}`);
      } finally {
        this.isSelectiveGiftActivity = false;
      }
    }
  }

  // 是否正在执行第X件Y折活动状态变更
  private isXPiecesYFold = false;

  /**
   * 第X件Y折活动状态变更
   */
  @Cron('*/1 * * * *')
  async xPiecesYFold() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isXPiecesYFold) {
        return;
      }
      this.isXPiecesYFold = true;
      try {
        Logger.debug(`定时器执行:第X件Y折活动状态变更`);
        await this.discountActivityService.xPiecesYFoldAll();
        Logger.debug(`定时器执行:第X件Y折活动状态变更完成`);
      } catch (error) {
        Logger.error(`定时器:第X件Y折活动状态变更错误:${error}`);
      } finally {
        this.isXPiecesYFold = false;
      }
    }
  }

  // 是否正在执行打包一口价活动状态变更
  private isPackageOnePrice = false;

  /**
   * 打包一口价活动状态变更
   */
  @Cron('*/1 * * * *')
  async packageOnePrice() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isPackageOnePrice) {
        return;
      }
      this.isPackageOnePrice = true;
      try {
        Logger.debug(`定时器执行:打包一口价活动状态变更`);
        await this.discountActivityService.packageOnePriceAll();
        Logger.debug(`定时器执行:打包一口价活动状态变更完成`);
      } catch (error) {
        Logger.error(`定时器:打包一口价活动状态变更错误:${error}`);
      } finally {
        this.isPackageOnePrice = false;
      }
    }
  }

  // 是否正在执行保存统计数据
  private isSaveStatistics = false;

  /**
   * 保存统计数据
   */
  @Cron('*/5 * * * *')
  async saveStatistics() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isSaveStatistics) {
        return;
      }
      this.isSaveStatistics = true;
      try {
        Logger.debug(`定时器执行:保存统计数据`);
        await this.commonService.saveStatistics();
        Logger.debug(`定时器执行:保存统计数据完成`);
      } catch (error) {
        Logger.error(`定时器:保存统计数据错误:${error}`);
      } finally {
        this.isSaveStatistics = false;
      }
    }
  }

  private isSaveOperationPlanCustomer = false;

  /**
   * 运营计划状态变更-保存计划中的用户
   */
  @Cron('*/1 * * * *')
  async operationPlanStateChange() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isSaveOperationPlanCustomer) {
        return;
      }
      this.isSaveOperationPlanCustomer = true;
      try {
        Logger.debug(`定时器执行:运营计划状态变更-保存计划中的用户`);
        await this.operationPlanService.operationPlanStateChange();
        Logger.debug(`定时器执行:运营计划状态变更-保存计划中的用户完成`);
      } catch (error) {
        Logger.error(`定时器:运营计划状态变更-保存计划中的用户错误:${error}`);
      } finally {
        this.isSaveOperationPlanCustomer = false;
      }
    }
  }

  private isExecuteOperationPlan = false;
  /**
   * 执行运营计划-发放优惠券
   */
  @Cron('*/1 * * * *')
  async executeOperationPlan() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isExecuteOperationPlan) {
        return;
      }
      this.isExecuteOperationPlan = true;
      try {
        Logger.debug(`定时器执行:执行运营计划-发放优惠券`);
        await this.operationPlanService.executeOperationPlan();
        Logger.debug(`定时器执行:执行运营计划-发放优惠券完成`);
      } catch (error) {
        Logger.error(`定时器:执行运营计划-发放优惠券错误:${error}`);
      } finally {
        this.isExecuteOperationPlan = false;
      }
    }
  }

  // 是否正在执行定时更新物流公司
  private isAsyncWechatLogisticsCompany = false;

  /**
   * 定时更新物流公司
   *
   */
  @Cron('0 0 * * *')
  async asyncWechatLogisticsCompany() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isAsyncWechatLogisticsCompany) {
        return;
      }
      this.isAsyncWechatLogisticsCompany = true;
      try {
        Logger.debug(`定时器执行:定时更新物流公司`);
        await this.wechatLogisticsCompanyService.asyncWechatLogisticsCompany();
        Logger.debug(`定时器执行:定时更新物流公司完成`);
      } catch (error) {
        Logger.error(`定时器:定时更新物流公司错误:${error}`);
      } finally {
        this.isAsyncWechatLogisticsCompany = false;
      }
    }
  }

  // 是否正在执行定时更新分销员明细
  private isAsyncDistributorDetail = false;

  /**
   * 定时更新分销员明细
   */
  @Cron('0 1 * * *')
  async asyncDistributorDetail() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isAsyncDistributorDetail) {
        return;
      }
      this.isAsyncDistributorDetail = true;
      try {
        Logger.debug(`定时器执行:定时更新分销员明细`);
        await this.distributorService.asyncDistributorDetail();
        Logger.debug(`定时器执行:定时更新分销员明细完成`);
      } catch (error) {
        Logger.error(`定时器:定时更新分销员明细错误:${error}`);
      } finally {
        this.isAsyncDistributorDetail = false;
      }
    }
  }

  /**
   * 定时获取matomo的actionID数据
   */
  // @Cron('0 0 * * *')
  // async asyncMatomoAction() {
  //   if (this.processContext.isWorker) {
  //     try {
  //       Logger.debug(`定时器执行:定时获取matomo的actionID数据`);
  //       await this.matomoService.asyncMatomoAction();
  //       Logger.debug(`定时器执行:定时获取matomo的actionID数据完成`);
  //     } catch (error) {
  //       Logger.error(`定时器:定时获取matomo的actionID数据错误:${error}`);
  //     }
  //   }
  // }

  // 是否正在执行定时清除完成的任务记录
  private isClearTaskRecord = false;

  /**
   * 定时清除完成的任务记录
   */
  @Cron('0 0 * * *')
  async clearTaskRecord() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isClearTaskRecord) {
        return;
      }
      this.isClearTaskRecord = true;
      try {
        Logger.debug(`定时器执行:定时清除完成的任务记录`);
        await this.commonService.clearTaskRecord();
        Logger.debug(`定时器执行:定时清除完成的任务记录完成`);
      } catch (error) {
        Logger.error(`定时器:定时清除完成的任务记录错误:${error}`);
      } finally {
        this.isClearTaskRecord = false;
      }
    }
  }
  private isRunningCompressImage = false;
  /**
   * 定时压缩图片
   */
  @Cron('*/5 * * * * *')
  async compressImage() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isRunningCompressImage) {
        return;
      }
      this.isRunningCompressImage = true;
      try {
        Logger.debug(`定时器执行:定时压缩图片`);
        await this.commonService.timingCompressImage();
        Logger.debug(`定时器执行:定时压缩图片完成`);
      } catch (error) {
        Logger.error(`定时器:定时压缩图片错误:${error}`);
      } finally {
        this.isRunningCompressImage = false;
      }
    }
  }

  //定时预处理图片
  private isRunningPreprocessImage = false;
  /**
   * 定时预处理图片
   */
  @Cron('*/5 * * * * *')
  async preprocessImage() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isRunningPreprocessImage) {
        return;
      }
      this.isRunningPreprocessImage = true;
      try {
        Logger.debug(`定时器执行:定时预处理图片`);
        await this.commonService.timingPreprocessImage();
        Logger.debug(`定时器执行:定时预处理图片完成`);
      } catch (error) {
        Logger.error(`定时器:定时预处理图片错误:${error}`);
      } finally {
        this.isRunningPreprocessImage = false;
      }
    }
  }
  private isUploadShippingInfo = false;
  /**
   * 定时添加需要上传的物流信息
   */
  @Cron('0 */5 * * * *')
  async orderShippingUploadRecord() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isUploadShippingInfo) {
        return;
      }
      this.isUploadShippingInfo = true;
      try {
        Logger.debug(`定时器执行:定时添加需要上传的物流信息`);
        await this.wechatLogisticsCompanyService.orderShippingUploadRecord();
        Logger.debug(`定时器执行:定时添加需要上传的物流信息完成`);
      } catch (error) {
        Logger.error(`定时器:定时添加需要上传的物流信息错误:${error}`);
      } finally {
        this.isUploadShippingInfo = false;
      }
    }
  }

  private isRunningUploadShipping = false;
  /**
   * 定时上传物流信息
   */
  @Cron('0 */1 * * * *')
  async runningUploadShipping() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isRunningUploadShipping) {
        return;
      }
      this.isRunningUploadShipping = true;
      try {
        Logger.debug(`定时器执行:定时上传物流信息`);
        await this.wechatLogisticsCompanyService.runningUploadShipping();
        Logger.debug(`定时器执行:定时上传物流信息完成`);
      } catch (error) {
        Logger.error(`定时器:定时上传物流信息错误:${error}`);
      } finally {
        this.isRunningUploadShipping = false;
      }
    }
  }

  private isAddVirtualDeliveryRecord = false;
  /**
   * 定时添加需要上传虚拟订单发货记录
   */
  @Cron('0 */1 * * * *')
  async virtualDeliveryRecord() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isAddVirtualDeliveryRecord) {
        return;
      }
      this.isAddVirtualDeliveryRecord = true;
      try {
        Logger.debug(`定时器执行:定时添加需要上传虚拟订单发货记录`);
        await this.wechatLogisticsCompanyService.virtualDeliveryRecord();
        Logger.debug(`定时器执行:定时添加需要上传虚拟订单发货记录完成`);
      } catch (error) {
        Logger.error(`定时器:定时添加需要上传虚拟订单发货记录错误:${error}`);
      } finally {
        this.isAddVirtualDeliveryRecord = false;
      }
    }
  }

  private isRunningVirtualDeliveryRecord = false;
  /**
   * 定时上传虚拟订单发货记录
   */
  @Cron('0 */1 * * * *')
  async runningVirtualDeliveryRecord() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isRunningVirtualDeliveryRecord) {
        return;
      }
      this.isRunningVirtualDeliveryRecord = true;
      try {
        Logger.debug(`定时器执行:定时上传虚拟订单发货记录`);
        await this.wechatLogisticsCompanyService.runningVirtualDeliveryRecord();
        Logger.debug(`定时器执行:定时上传虚拟订单发货记录完成`);
      } catch (error) {
        Logger.error(`定时器:定时上传虚拟订单发货记录错误:${error}`);
      } finally {
        this.isRunningVirtualDeliveryRecord = false;
      }
    }
  }

  // 是否正在执行验证优惠券礼包状态
  private isValidateCouponBundleState = false;

  /**
   * 验证优惠券礼包状态
   */
  @Cron('*/1 * * * *')
  async validateCouponBundleState() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isValidateCouponBundleState) {
        return;
      }
      this.isValidateCouponBundleState = true;
      try {
        Logger.debug(`定时器执行:验证优惠券礼包状态`);
        await this.couponBundleService.validateCouponBundleStateAll();
        Logger.debug(`定时器执行:验证优惠券礼包状态完成`);
      } catch (error) {
        Logger.error(`定时器:验证优惠券礼包状态错误:${error}`);
      } finally {
        this.isValidateCouponBundleState = false;
      }
    }
  }

  // 是否正在执行验证新客福利状态
  private isValidateFirstCustomerBenefitState = false;

  /**
   * 验证新客福利状态
   */
  @Cron('*/1 * * * *')
  async validateFirstCustomerBenefitState() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isValidateFirstCustomerBenefitState) {
        return;
      }
      this.isValidateFirstCustomerBenefitState = true;
      try {
        Logger.debug(`定时器执行:验证新客福利状态`);
        await this.firstCustomerBenefitService.validateFirstCustomerBenefitStateAll();
        Logger.debug(`定时器执行:验证新客禠状态完成`);
      } catch (error) {
        Logger.error(`定时器:验证新客福利状态错误:${error}`);
      } finally {
        this.isValidateFirstCustomerBenefitState = false;
      }
    }
  }

  // 是否正在执行积分商品状态变更
  private isPointsConfigStateChange = false;

  /**
   * 积分商品状态变更
   */
  @Cron('*/1 * * * *')
  async pointsConfigStateChange() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isPointsConfigStateChange) {
        return;
      }
      this.isPointsConfigStateChange = true;
      try {
        Logger.debug(`定时器执行:积分商品状态变更`);
        await this.pointsProductService.pointsConfigStateChangeAll();
        Logger.debug(`定时器执行:积分商品状态变更完成`);
      } catch (error) {
        Logger.error(`定时器:积分商品状态变更错误:${error}`);
      } finally {
        this.isPointsConfigStateChange = false;
      }
    }
  }

  // 修改盲盒活动状态
  private isBlindBoxActivityStateChange = false;

  /**
   * 盲盒活动状态变更
   */
  @Cron('*/1 * * * *')
  async blindBoxActivityStateChange() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isBlindBoxActivityStateChange) {
        return;
      }
      this.isBlindBoxActivityStateChange = true;
      try {
        Logger.debug(`定时器执行:盲盒活动状态变更`);
        await this.blindBoxActivityService.blindBoxActivityStateChangeAll();
        Logger.debug(`定时器执行:盲盒活动状态变更完成`);
      } catch (error) {
        Logger.error(`定时器:盲盒活动状态变更错误:${error}`);
      } finally {
        this.isBlindBoxActivityStateChange = false;
      }
    }
  }

  // 修改盲盒助力结束状态
  private isBlindBoxOrderAssistStateChange = false;

  /**
   * 盲盒助力结束状态变更
   */
  @Cron('*/1 * * * *')
  async blindBoxOrderAssistStateChange() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isBlindBoxOrderAssistStateChange) {
        return;
      }
      this.isBlindBoxOrderAssistStateChange = true;
      try {
        Logger.debug(`定时器执行:盲盒助力结束状态变更`);
        await this.blindBoxOrderService.blindBoxOrderAssistStateChangeAll();
        Logger.debug(`定时器执行:盲盒助力结束状态变更完成`);
      } catch (error) {
        Logger.error(`定时器:盲盒助力结束状态变更错误:${error}`);
      } finally {
        this.isBlindBoxOrderAssistStateChange = false;
      }
    }
  }

  // 盲盒活动预约提醒
  private isBlindBoxActivityRemind = false;

  /**
   * 盲盒活动预约提醒
   */
  @Cron('*/1 * * * *')
  async blindBoxActivityRemind() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isBlindBoxActivityRemind) {
        return;
      }
      this.isBlindBoxActivityRemind = true;
      try {
        Logger.debug(`定时器执行:盲盒活动预约提醒`);
        await this.blindBoxActivityService.blindBoxActivityRemindAll();
        Logger.debug(`定时器执行:盲盒活动预约提醒完成`);
      } catch (error) {
        Logger.error(`定时器:盲盒活动预约提醒错误:${error}`);
      } finally {
        this.isBlindBoxActivityRemind = false;
      }
    }
  }

  // 购物金发放活动
  private isShoppingCreditClaim = false;
  /**
   * 购物金发放活动
   */
  @Cron('*/1 * * * *')
  async shoppingCreditClaim() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isShoppingCreditClaim) {
        return;
      }
      this.isShoppingCreditClaim = true;
      try {
        Logger.debug(`定时器执行:购物金发放活动`);
        await this.shoppingCreditsClaimActivityService.shoppingCreditClaimStateChangeAll();
        Logger.debug(`定时器执行:购物金发放活动完成`);
      } catch (error) {
        Logger.error(`定时器:购物金发放活动错误:${error}`);
      } finally {
        this.isShoppingCreditClaim = false;
      }
    }
  }

  // 购物金兑换活动
  private isShoppingCreditExchange = false;
  /**
   * 购物金兑换活动
   */
  @Cron('*/1 * * * *')
  async shoppingCreditExchange() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isShoppingCreditExchange) {
        return;
      }
      this.isShoppingCreditExchange = true;
      try {
        Logger.debug(`定时器执行:购物金兑换活动`);
        await this.shoppingCreditsDeductionActivityService.shoppingCreditExchangeStateChangeAll();
        Logger.debug(`定时器执行:购物金兑换活动完成`);
      } catch (error) {
        Logger.error(`定时器:购物金兑换活动错误:${error}`);
      } finally {
        this.isShoppingCreditExchange = false;
      }
    }
  }

  // 购物金过期判断
  private isShoppingCreditValidPeriod = false;
  /**
   * 购物金过期判断 每天凌晨0点执行
   */
  @Cron('0 0 * * *')
  async shoppingCreditValidPeriod() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isShoppingCreditValidPeriod) {
        return;
      }
      this.isShoppingCreditValidPeriod = true;
      try {
        Logger.debug(`定时器执行:购物金过期判断`);
        await this.shoppingCreditsConfigService.shoppingCreditValidPeriodAll();
        Logger.debug(`定时器执行:购物金过期判断完成`);
      } catch (error) {
        Logger.error(`定时器:购物金过期判断错误:${error}`);
      } finally {
        this.isShoppingCreditValidPeriod = false;
      }
    }
  }

  // 每天晚上统计当天有订单的用户交易
  private isDailyOrderUserStatistics = false;

  /**
   * 每天晚上统计当天有订单的用户交易
   */
  @Cron('0 1 * * *')
  async dailyOrderUserStatistics() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isDailyOrderUserStatistics) {
        return;
      }
      this.isDailyOrderUserStatistics = true;
      try {
        Logger.debug(`定时器执行:每天晚上统计当天有订单的用户交易`);
        await this.customerSummaryService.dailyOrderUserStatisticsAll();
        Logger.debug(`定时器执行:每天晚上统计当天有订单的用户交易完成`);
      } catch (error) {
        Logger.error(`定时器:每天晚上统计当天有订单的用户交易错误:${error}`);
      } finally {
        this.isDailyOrderUserStatistics = false;
      }
    }
  }

  private isExecuteImportExternalOrder = false;
  /**
   * 执行导入外部订单
   */
  @Cron(`${((Math.floor(Math.random() * 100000) % 59) + '').padStart(2, '0')} * * * * *`)
  async executeImportExternalOrder() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isExecuteImportExternalOrder) {
        return;
      }
      this.isExecuteImportExternalOrder = true;
      try {
        Logger.debug(`定时器执行:处理外部订单导入`);
        await this.externalOrderService.executeImportExternalOrder();
        Logger.debug(`定时器执行:处理外部订单导入完成`);
      } catch (error) {
        Logger.error(`定时器:处理外部订单导入错误:${error}`);
      } finally {
        this.isExecuteImportExternalOrder = false;
      }
    }
  }

  // 修改心愿盒子活动状态
  private isWishBoxActivityStateChange = false;

  /**
   * 心愿盒子活动状态变更
   */
  @Cron(`${((Math.floor(Math.random() * 100000) % 59) + '').padStart(2, '0')} * * * * *`)
  async wishBoxActivityRemind() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isWishBoxActivityStateChange) {
        return;
      }
      this.isWishBoxActivityStateChange = true;
      try {
        Logger.debug(`定时器执行:心愿盒子活动预约提醒`);
        await this.wishBoxService.wishBoxActivityRemindAll();
        Logger.debug(`定时器执行:心愿盒子活动预约提醒完成`);
      } catch (error) {
        Logger.error(`定时器:心愿盒子活动预约提醒错误:${error}`);
      } finally {
        this.isWishBoxActivityStateChange = false;
      }
    }
  }

  // 是否正在执行优惠券到期提醒
  private isSendingCouponExpiredNotice = false;

  /**
   * 优惠券到期提醒定时任务
   * 每天凌晨2点执行，检查24小时内即将到期的优惠券
   */
  @Cron('0 2 * * *')
  async sendCouponExpiredNotice() {
    if (this.processContext.isWorker) {
      if (this.isDisableCron) {
        return;
      }
      if (this.isSendingCouponExpiredNotice) {
        return;
      }
      this.isSendingCouponExpiredNotice = true;
      try {
        Logger.debug('定时器执行：优惠券到期提醒通知');
        const result = await this.couponService.processCouponExpiredNotifications();
        Logger.debug(`定时器执行：优惠券到期提醒通知完成: ${result.message}`);
      } catch (error) {
        Logger.error(`定时器:优惠券到期提醒通知错误:${error}`);
      } finally {
        this.isSendingCouponExpiredNotice = false;
      }
    }
  }

  /**
   * 手动执行优惠券到期提醒
   */
  async manualSendCouponExpiredNotice() {
    Logger.debug('手动执行：优惠券到期提醒通知');
    try {
      const result = await this.couponService.processCouponExpiredNotifications();
      Logger.debug('手动执行：优惠券到期提醒通知完成');
      return result;
    } catch (error) {
      Logger.error(`手动执行优惠券到期提醒错误:${error}`);
      return {success: false, message: `执行失败: ${error.message}`};
    }
  }
}
