import {Injectable, Logger} from '@nestjs/common';
import {CacheService, DEFAULT_CACHE_TIMEOUT, KvsService, MemoryStorageService} from '@scmally/kvs';
import {RedLockService} from '@scmally/red-lock';
import {VirtualCurrencyService} from '@scmally/virtual-currency';
import {ShoppingCreditConditionType, VirtualCurrencyCode, VirtualCurrencySourceType} from '../generated-admin-types';
import {
  ChannelService,
  ID,
  LanguageCode,
  ListQueryBuilder,
  ListQueryOptions,
  Order,
  ProductService,
  Promotion,
  PromotionService,
  RelationPaths,
  RequestContext,
  Transaction,
  TransactionalConnection,
} from '@vendure/core';
import {generatePublicId} from '@vendure/core/dist/common/generate-public-id';
import NP from 'number-precision';
import {In} from 'typeorm';
import {OrderPromotionResult, ShoppingCreditsClaimActivity} from '../entities';
import {
  ActivityStatus,
  ApplicableProduct,
  ApplicableType,
  DeletionResult,
  DiscountType,
  ProgramLinkInput,
  PromotionType,
  RuleType,
  ShoppingCreditsClaimActivityInput,
} from '../generated-admin-types';
import {SettingKey, ShoppingMoneyGrantTiming, SymbolType} from '../generated-shop-types';
import {ActivityProduct, ActivityUtils} from '../utils';
import {CommonService} from './common.service';
import {CustomPromotionService} from './custom-promotion.service';
import {OrderPromotionResultService} from './order-promotion-result.service';
import {ProductCustomService} from './product-custom.service';
import {ProductPromotionActiveService} from './product-promotion-active.service';
import {PromotionResultDetailService} from './promotion-result-detail.service';
import {SettingService} from './setting.service';
@Injectable()
export class ShoppingCreditsClaimActivityService {
  constructor(
    private connection: TransactionalConnection,
    private cacheService: CacheService,
    private memoryStorageService: MemoryStorageService,
    private kvsService: KvsService,
    private listQueryBuilder: ListQueryBuilder,
    private customPromotionService: CustomPromotionService,
    private promotionResultDetailService: PromotionResultDetailService,
    private productPromotionActiveService: ProductPromotionActiveService,
    private promotionService: PromotionService,
    private productService: ProductService,
    private channelService: ChannelService,
    private orderPromotionResultService: OrderPromotionResultService,
    private settingService: SettingService,
    private virtualCurrencyService: VirtualCurrencyService,
    private redLockService: RedLockService,
    private productCustomService: ProductCustomService,
    private commonService: CommonService,
  ) {}

  async getShoppingCreditsClaimLink(ctx: RequestContext, input: ProgramLinkInput) {
    const shoppingCreditsClaimId = input?.id;
    const shoppingCreditsClaimActivity = await this.findOne(ctx, shoppingCreditsClaimId);
    if (!shoppingCreditsClaimActivity) {
      throw new Error(`活动不存在`);
    }
    const urlLink = await this.commonService.generateSmallProgramLink(
      ctx,
      {
        id: String(shoppingCreditsClaimActivity.promotionId),
        type: input.type,
      },
      input.path ?? '',
    );
    return urlLink;
  }

  async deleteShoppingCreditsClaimActivity(ctx: RequestContext, shoppingCreditsClaimActivityId: ID) {
    const shoppingCreditsClaimActivity = await this.findOne(ctx, shoppingCreditsClaimActivityId);
    if (!shoppingCreditsClaimActivity) {
      throw new Error(`活动不存在`);
    }
    shoppingCreditsClaimActivity.deletedAt = new Date();
    shoppingCreditsClaimActivity.status = ActivityStatus.Failure;
    const promotionId = shoppingCreditsClaimActivity.promotionId;
    if (promotionId) {
      await this.connection.getRepository(ctx, Promotion).update(promotionId, {
        enabled: false,
      });
      await this.promotionService.softDeletePromotion(ctx, promotionId);
    }
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, ShoppingCreditsClaimActivity).save(shoppingCreditsClaimActivity);
    await this.cacheService.removeCache([
      `Query:ShoppingCreditsClaimActivity:${shoppingCreditsClaimActivityId}:${ctx.channelId}`,
      `Query:Promotion:${promotionId}:${ctx.channelId}`,
      `Query:Promotion:IncludeFailure:${promotionId}:${ctx.channelId}`,
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(
      ctx,
      shoppingCreditsClaimActivity.applicableProduct,
    );
    return {
      result: DeletionResult.Deleted,
      message: 'delete success',
    };
  }
  async failureShoppingCreditsClaimActivity(ctx: RequestContext, shoppingCreditsClaimActivityId: ID) {
    const shoppingCreditsClaimActivity = await this.findOne(ctx, shoppingCreditsClaimActivityId);
    if (!shoppingCreditsClaimActivity) {
      throw new Error(`活动不存在`);
    }
    if (shoppingCreditsClaimActivity.status === ActivityStatus.Failure) {
      throw new Error(`活动已失效`);
    }
    shoppingCreditsClaimActivity.status = ActivityStatus.Failure;
    const promotionId = shoppingCreditsClaimActivity.promotionId;
    if (promotionId) {
      await this.connection.getRepository(ctx, Promotion).update(promotionId, {
        enabled: false,
      });
    }
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, ShoppingCreditsClaimActivity).save(shoppingCreditsClaimActivity);
    await this.cacheService.removeCache([
      `Query:ShoppingCreditsClaimActivity:${shoppingCreditsClaimActivityId}:${ctx.channelId}`,
      `Query:Promotion:${promotionId}:${ctx.channelId}`,
      `Query:Promotion:IncludeFailure:${promotionId}:${ctx.channelId}`,
    ]);
    await this.productPromotionActiveService.activeProductCacheClear(
      ctx,
      shoppingCreditsClaimActivity.applicableProduct,
    );
  }
  async upsertShoppingCreditsClaimActivity(ctx: RequestContext, input: ShoppingCreditsClaimActivityInput) {
    await this.validate(ctx, input);
    let oldApplicableProduct: ApplicableProduct | undefined;
    if (input.id) {
      const shoppingCreditsClaimActivity = await this.findOne(ctx, input.id);
      if (!shoppingCreditsClaimActivity) {
        throw new Error(`活动不存在`);
      }
      oldApplicableProduct = shoppingCreditsClaimActivity.applicableProduct;
    }
    let status = ActivityStatus.Normal;
    if (new Date() < new Date(input.startTime)) {
      status = ActivityStatus.NotStarted;
    }
    if (new Date() > new Date(input.endTime)) {
      status = ActivityStatus.HaveEnded;
    }
    let shoppingCreditsClaimActivity = new ShoppingCreditsClaimActivity({
      ...(input as ShoppingCreditsClaimActivity),
      status,
    });
    shoppingCreditsClaimActivity = await this.channelService.assignToCurrentChannel(shoppingCreditsClaimActivity, ctx);
    shoppingCreditsClaimActivity = await this.connection
      .getRepository(ctx, ShoppingCreditsClaimActivity)
      .save(shoppingCreditsClaimActivity);
    const promotion = await this.upsertPromotion(ctx, shoppingCreditsClaimActivity);
    await this.productPromotionActiveService.createProductPromotionActive(
      ctx,
      promotion,
      input.applicableProduct as ApplicableProduct,
      oldApplicableProduct,
    );
    shoppingCreditsClaimActivity.promotion = promotion;
    await this.kvsService.activeTime.set(String(ctx.channelId), new Date());
    await this.connection.getRepository(ctx, ShoppingCreditsClaimActivity).save(shoppingCreditsClaimActivity);
    if (input.id) {
      await this.cacheService.removeCache([
        `Query:ShoppingCreditsClaimActivity:${input.id}:${ctx.channelId}`,
        `Query:Promotion:${promotion.id}:${ctx.channelId}`,
        `Query:Promotion:IncludeFailure:${promotion.id}:${ctx.channelId}`,
      ]);
    }
    return this.findOne(ctx, shoppingCreditsClaimActivity.id);
  }
  async upsertPromotion(ctx: RequestContext, shoppingCreditsClaimActivity: ShoppingCreditsClaimActivity) {
    const type = PromotionType.ShoppingCreditsClaim;
    const promotionInput = {
      couponCode: shoppingCreditsClaimActivity.promotion
        ? shoppingCreditsClaimActivity.promotion.couponCode
        : generatePublicId(),
      name: shoppingCreditsClaimActivity.displayName,
      startsAt: shoppingCreditsClaimActivity.startTime,
      endsAt: shoppingCreditsClaimActivity.endTime,
      enabled:
        shoppingCreditsClaimActivity.status === ActivityStatus.Normal ||
        shoppingCreditsClaimActivity.status === ActivityStatus.NotStarted,
      conditions: [],
      actions: [],
      customFields: {
        type: type,
        isAutomatic: false,
        activityName: shoppingCreditsClaimActivity.displayName,
      },
      translations: [
        {
          languageCode: LanguageCode.zh,
          name: shoppingCreditsClaimActivity.displayName,
        },
      ],
    };
    shoppingCreditsClaimActivity = (await this.findOne(
      ctx,
      shoppingCreditsClaimActivity.id,
    )) as ShoppingCreditsClaimActivity;
    if (shoppingCreditsClaimActivity.promotionId) {
      const updatePromotion = {
        ...promotionInput,
        id: shoppingCreditsClaimActivity.promotionId,
        customFields: {
          type: type,
          isAutomatic: false,
          stackingDiscountSwitch: shoppingCreditsClaimActivity.stackingDiscountSwitch,
          stackingPromotionTypes: shoppingCreditsClaimActivity.stackingPromotionTypes,
          activityName: shoppingCreditsClaimActivity.displayName,
        },
      };
      return (await this.promotionService.updatePromotion(ctx, updatePromotion)) as Promotion;
    } else {
      const promotion = (await this.promotionService.createPromotion(ctx, promotionInput)) as Promotion;
      await this.connection.getRepository(ctx, Promotion).update(promotion.id, {
        customFields: {
          type: type,
          isAutomatic: false,
          stackingDiscountSwitch: shoppingCreditsClaimActivity.stackingDiscountSwitch,
          stackingPromotionTypes: shoppingCreditsClaimActivity.stackingPromotionTypes,
          activityName: shoppingCreditsClaimActivity.displayName,
        },
      });
      return promotion;
    }
  }
  async validate(ctx: RequestContext, input: ShoppingCreditsClaimActivityInput) {
    if (input.startTime > input.endTime) {
      throw new Error('开始时间不能大于结束时间');
    }
    const applicableProduct = input.applicableProduct;
    if (applicableProduct?.applicableType !== ApplicableType.All) {
      if (!applicableProduct?.productIds || applicableProduct.productIds.length === 0) {
        throw new Error('请选择商品');
      }
    }
    const ruleValues = input.ruleValues ?? [];
    for (const ruleValue of ruleValues) {
      if ((!ruleValue?.minimum && ruleValue?.minimum !== 0) || ruleValue.minimum < 0) {
        throw new Error('请输入正确的活动门槛金额');
      }
      if (ruleValue.discountValue?.discountType === DiscountType.FixedAmount) {
        if (
          (!ruleValue.discountValue?.discount && ruleValue.discountValue?.discount !== 0) ||
          ruleValue.discountValue?.discount < 0
        ) {
          throw new Error('请输入正确的发放购物金的金额');
        }
      } else if (ruleValue.discountValue?.discountType === DiscountType.FixedPercent) {
        if (
          (!ruleValue.discountValue?.discount && ruleValue.discountValue?.discount !== 0) ||
          ruleValue.discountValue?.discount < 0 ||
          ruleValue.discountValue?.discount > 100
        ) {
          throw new Error('请输入正确的发放购物金的百分比');
        }
      }
      // if (input.ruleType === RuleType.Cycle) {
      //   // 循环不能设置百分比
      //   if (ruleValue.discountValue?.discountType === DiscountType.FixedPercent) {
      //     throw new Error('循环规则不能设置百分比');
      //   }
      // }
    }

    const inputProduct: ActivityProduct = {
      startTime: input.startTime,
      endTime: input.endTime,
      applicableType: input.applicableProduct?.applicableType as ApplicableType,
      productIds: input.applicableProduct?.productIds as ID[],
    };
    // 验证活动商品和当前活动是否有冲突
    await this.verifyProductIdsAndShoppingCreditsClaimActivityConflict(ctx, inputProduct, input?.id as ID);
  }

  async verifyProductIdsAndShoppingCreditsClaimActivityConflict(
    ctx: RequestContext,
    inputProduct: ActivityProduct,
    shoppingCreditsClaimActivityId?: ID,
  ) {
    const shoppingCreditsClaimActivities = await this.getNotFailureShoppingCreditsClaimActivity(
      ctx,
      shoppingCreditsClaimActivityId,
    );
    for (const shoppingCreditsClaimActivity of shoppingCreditsClaimActivities) {
      const shoppingCreditsClaimProduct: ActivityProduct = {
        startTime: shoppingCreditsClaimActivity.startTime,
        endTime: shoppingCreditsClaimActivity.endTime,
        productIds: shoppingCreditsClaimActivity.applicableProduct.productIds as ID[],
        applicableType: shoppingCreditsClaimActivity.applicableProduct.applicableType,
      };
      // 判断活动商品是否冲突
      const productId = ActivityUtils.isProductIdConflictWithOtherActivity(shoppingCreditsClaimProduct, inputProduct);
      if (
        productId &&
        // 判断活动时间是否冲突
        ActivityUtils.isTimeOverlappingWithOtherActivity(shoppingCreditsClaimProduct, inputProduct)
      ) {
        if (productId === -1) {
          throw new Error(`活动商品和购物金赠送活动商品冲突,存在活动商品为全部商品的活动`);
        }
        const product = await this.productService.findOne(ctx, productId);
        throw new Error(`活动商品${product?.name}和购物金赠送活动商品冲突`);
      }
    }
  }

  // 获取未失效的活动
  async getNotFailureShoppingCreditsClaimActivity(ctx: RequestContext, shoppingCreditsClaimActivityId?: ID) {
    const qb = this.listQueryBuilder.build(
      ShoppingCreditsClaimActivity,
      {},
      {
        relations: ['promotion'],
        ctx,
        channelId: ctx.channelId,
      },
    );
    if (shoppingCreditsClaimActivityId) {
      qb.andWhere(`${qb.alias}.id != :shoppingCreditsClaimActivityId`, {
        shoppingCreditsClaimActivityId,
      });
    }
    // 活动状态不能为结束和失效
    qb.andWhere(`${qb.alias}.status not in (:...status)`, {
      status: [ActivityStatus.Failure, ActivityStatus.HaveEnded],
    });
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    const shoppingCreditsClaimActivities = await qb.getMany();
    return shoppingCreditsClaimActivities;
  }

  async findAll(
    ctx: RequestContext,
    options?: ListQueryOptions<ShoppingCreditsClaimActivity>,
    relations?: RelationPaths<ShoppingCreditsClaimActivity>,
    isAdmin = false,
  ) {
    const qb = this.listQueryBuilder.build(ShoppingCreditsClaimActivity, options, {
      relations: relations ?? ['promotion'],
      channelId: ctx.channelId,
      ctx,
    });
    qb.andWhere(`${qb.alias}.deletedAt IS NULL`);
    qb.leftJoinAndSelect(`${qb.alias}.promotion`, 'promotion');
    const [items, totalItems] = await qb.getManyAndCount();
    return {
      items,
      totalItems,
    };
  }
  async findOne(
    ctx: RequestContext,
    shoppingCreditsClaimActivityId: ID,
    options?: ListQueryOptions<ShoppingCreditsClaimActivity>,
    relations?: RelationPaths<ShoppingCreditsClaimActivity>,
  ) {
    let shoppingCreditsClaimActivities: ShoppingCreditsClaimActivity[] | undefined;
    const memoryStorageCacheKey = `Query:ShoppingCreditsClaimActivity:${shoppingCreditsClaimActivityId}:${ctx.channelId}`;
    if (ctx.apiType === 'shop') {
      shoppingCreditsClaimActivities = this.memoryStorageService.get(memoryStorageCacheKey);
    }
    if (!shoppingCreditsClaimActivities) {
      const qb = this.listQueryBuilder.build(ShoppingCreditsClaimActivity, options, {
        relations: [],
        channelId: ctx.channelId,
        ctx,
      });
      qb.andWhere(`${qb.alias}.id = :shoppingCreditsClaimActivityId`, {
        shoppingCreditsClaimActivityId,
      });
      if (ctx.apiType === 'shop') {
        qb.cache(memoryStorageCacheKey, DEFAULT_CACHE_TIMEOUT);
      }
      shoppingCreditsClaimActivities = await qb.getMany();
      if (ctx.apiType === 'shop') {
        this.memoryStorageService.set(memoryStorageCacheKey, shoppingCreditsClaimActivities);
      }
      if (shoppingCreditsClaimActivities?.length > 0) {
        const shoppingCreditsClaimActivity = shoppingCreditsClaimActivities[0];
        const promotionId = shoppingCreditsClaimActivity.promotionId;
        if (promotionId) {
          const promotion = await this.customPromotionService.getPromotionByPromotionId(ctx, promotionId);
          shoppingCreditsClaimActivity.promotion = promotion as Promotion;
        }
        return shoppingCreditsClaimActivity;
      }
    }
    return shoppingCreditsClaimActivities[0];
  }

  @Transaction()
  // 购物金发放
  async orderShoppingCreditsClaimActivity(ctx: RequestContext, order: Order) {
    const orderId = order.id;
    const lock = await this.redLockService.lockResource(
      `Customer—VirtualCurrency-${VirtualCurrencyCode.ShoppingCredits}-${orderId}-${ctx.channelId}`,
    );
    try {
      const promotionResult = await this.orderPromotionResultService.getResultByOrderId(ctx, orderId);
      if (!promotionResult) {
        return;
      }
      // 根据优惠结果判断是否有购物金需要发放
      let shoppingCreditsClaim = promotionResult.promResult?.shoppingCreditsClaim;
      if (!shoppingCreditsClaim || shoppingCreditsClaim <= 0) {
        return;
      }
      // 判断是否已经发放过购物金
      const history = await this.virtualCurrencyService.queryHistoryExist(
        ctx,
        SymbolType.In,
        VirtualCurrencyCode.ShoppingCredits,
        VirtualCurrencySourceType.Order,
        orderId as string,
        order.customerId,
      );
      if (history) {
        Logger.error('已经发放过购物金');
        return;
      }
      shoppingCreditsClaim = await this.computeNonRefundedShoppingCredits(
        ctx,
        promotionResult,
        shoppingCreditsClaim,
        orderId,
      );
      await this.virtualCurrencyService.addCurrency(
        ctx,
        VirtualCurrencyCode.ShoppingCredits,
        shoppingCreditsClaim,
        '订单支付完成购物金发放',
        VirtualCurrencySourceType.Order,
        orderId as string,
        order.customerId,
      );
    } catch (error) {
      Logger.error('购物金发放失败', error);
      throw new Error('购物金发放失败');
    } finally {
      await this.redLockService.unlockResource(lock);
    }
  }
  // 获取未退款部分的可领购物金
  async computeNonRefundedShoppingCredits(
    ctx: RequestContext,
    promotionResult: OrderPromotionResult,
    shoppingCreditsClaim: number,
    orderId: ID,
  ): Promise<number> {
    const orderLineIds = await this.orderPromotionResultService.getRefundedOrderLineIds(ctx, orderId);
    if (orderLineIds.length === 0) {
      return shoppingCreditsClaim;
    }

    const orderLineIdSet = new Set(orderLineIds.map(id => id?.toString()));

    const filteredPromLineResults =
      promotionResult?.promResult?.promLineResults?.filter(
        promLineResult => promLineResult?.type === PromotionType.ShoppingCreditsClaim,
      ) ?? [];

    return filteredPromLineResults.reduce((newShoppingCreditsClaim, shoppingCreditsClaimActivity) => {
      const orderLines = shoppingCreditsClaimActivity?.orderLines ?? [];
      return orderLines.reduce((subtotal, orderLine) => {
        return orderLine && !orderLineIdSet.has(orderLine.orderLineId?.toString() ?? '')
          ? NP.plus(subtotal, orderLine.claimShoppingCredits ?? 0)
          : subtotal;
      }, newShoppingCreditsClaim);
    }, 0);
  }

  // 订单支付完成处理购物金
  async paymentSettledShoppingCreditsClaimActivity(ctx: RequestContext, order: Order) {
    // 购物金发放时机
    const setting = await this.settingService.setting(ctx, SettingKey.ShoppingMoneyGrantTiming);
    if (setting?.value === ShoppingMoneyGrantTiming.PaymentSuccess) {
      // 调用购物金发放
      await this.orderShoppingCreditsClaimActivity(ctx, order);
    }
  }

  async deliveryShoppingCreditsClaimActivity(ctx: RequestContext, order: Order) {
    // 购物金发放时机
    const setting = await this.settingService.setting(ctx, SettingKey.ShoppingMoneyGrantTiming);
    if (!setting || setting?.value !== ShoppingMoneyGrantTiming.PaymentSuccess) {
      // 调用购物金发放
      await this.orderShoppingCreditsClaimActivity(ctx, order);
    }
  }

  // 获取每个订单项可领取购物金
  async getOrderItemShoppingCreditsClaim(ctx: RequestContext, orderId: ID, merchantVoluntaryRefundId?: ID) {
    const promotionResult = await this.orderPromotionResultService.getResultByOrderId(ctx, orderId);
    if (!promotionResult) {
      return [];
    }
    const shoppingCreditsClaim = promotionResult.promResult?.shoppingCreditsClaim;
    if (!shoppingCreditsClaim) {
      return [];
    }
    // 先判断是否已经存在购物金发放记录
    const claimHistory = await this.virtualCurrencyService.queryHistoryExist(
      ctx,
      SymbolType.In,
      VirtualCurrencyCode.ShoppingCredits,
      VirtualCurrencySourceType.Order,
      orderId as string,
      promotionResult.order.customerId as string,
    );
    if (!claimHistory) {
      return [];
    }
    const promLineResults = promotionResult.promResult?.promLineResults;
    const shoppingCreditsClaims = promLineResults?.filter(item => item?.type === PromotionType.ShoppingCreditsClaim);
    // 遍历获取可领取的购物金
    const shoppingCreditsClaimActivities = [];
    if (shoppingCreditsClaims && shoppingCreditsClaims?.length > 0) {
      for (const shoppingCreditsClaimActivity of shoppingCreditsClaims) {
        const orderLines = shoppingCreditsClaimActivity?.orderLines;
        if (orderLines && orderLines?.length > 0) {
          for (const orderLine of orderLines) {
            shoppingCreditsClaimActivities.push({
              orderLineId: orderLine?.orderLineId,
              shoppingCreditsClaim: orderLine?.claimShoppingCredits,
            });
          }
        }
      }
    }
    //获取已经售后成功或者已经主动退款的订单项id
    const orderLineIds = await this.orderPromotionResultService.getRefundedOrderLineIds(
      ctx,
      orderId,
      merchantVoluntaryRefundId,
    );
    const orderLineIdSet = new Set(orderLineIds.map(id => id?.toString()));
    const shoppingCreditsClaimActivitiesFilter = shoppingCreditsClaimActivities.filter(
      // item => !orderLineIdSet.has(item.orderLineId ?? ''?.toString()),
      item => !orderLineIdSet.has(item?.orderLineId?.toString() ?? ''),
    );
    return shoppingCreditsClaimActivitiesFilter;
  }

  async activitySynopsis(ctx: RequestContext, shoppingCreditsClaimActivity: ShoppingCreditsClaimActivity) {
    const ruleValues = shoppingCreditsClaimActivity.ruleValues;
    const ruleType = shoppingCreditsClaimActivity.ruleType;
    let synopsisStr = '';
    const synopsisTags: string[] = [];
    const unit = shoppingCreditsClaimActivity.type === ShoppingCreditConditionType.QuantityFullReduction ? '件' : '元'; // 购物金发放的单位，按件或者按元来计算
    const unitScale = shoppingCreditsClaimActivity.type === ShoppingCreditConditionType.QuantityFullReduction ? 1 : 100;
    if (ruleType === RuleType.Ladder) {
      const maxShoppingCreditsClaim = ruleValues[ruleValues.length - 1];
      if (maxShoppingCreditsClaim.discountValue?.discountType === DiscountType.FixedAmount) {
        synopsisStr = `最高可得购物金${Number(maxShoppingCreditsClaim?.discountValue?.discount ?? 0) / 100}`;
        ruleValues.forEach(item => {
          if (item?.discountValue?.discountType === DiscountType.FixedAmount) {
            const commonStr = `满${item?.minimum / unitScale}${unit}可得购物金${
              Number(item?.discountValue?.discount ?? 0) / 100
            }`;
            synopsisTags.push(commonStr);
          } else {
            //买满50元,可返商品实付金额的10%购物金
            const commonStr = `满${item?.minimum / unitScale}${unit}可返实付金额${Number(
              item?.discountValue?.discount ?? 0,
            )}%的购物金`;
            synopsisTags.push(commonStr);
          }
        });
      } else if (maxShoppingCreditsClaim.discountValue?.discountType === DiscountType.FixedPercent) {
        //买满50元,可返商品实付金额的10%购物金
        synopsisStr = `最高可返商品实付金额${Number(maxShoppingCreditsClaim?.discountValue?.discount ?? 0)}%的购物金`;
        ruleValues.forEach(item => {
          if (item?.discountValue?.discountType === DiscountType.FixedPercent) {
            const commonStr = `满${item?.minimum / unitScale}${unit}可返实付金额${Number(
              item?.discountValue?.discount ?? 0,
            )}%的购物金`;
            synopsisTags.push(commonStr);
          } else {
            //买满50元,可返商品实付金额的10%购物金
            const commonStr = `满${item?.minimum / unitScale}${unit}可得购物金${
              Number(item?.discountValue?.discount ?? 0) / 100
            }`;
            synopsisTags.push(commonStr);
          }
        });
      }
    } else {
      const maxShoppingCreditsClaim = ruleValues[0];
      if (maxShoppingCreditsClaim.discountValue?.discountType === DiscountType.FixedAmount) {
        synopsisStr = `每满${maxShoppingCreditsClaim?.minimum / unitScale}${unit}可得购物金${
          Number(maxShoppingCreditsClaim?.discountValue?.discount ?? 0) / 100
        },最高可得购物金${Number(maxShoppingCreditsClaim?.maximumOffer) / 100}`;
      } else {
        synopsisStr = `每满${maxShoppingCreditsClaim?.minimum / unitScale}${unit}可返商品实付金额${Number(
          maxShoppingCreditsClaim?.discountValue?.discount ?? 0,
        )}%的购物金,最高可得购物金${Number(maxShoppingCreditsClaim?.maximumOffer) / 100}`;
      }
    }
    return {
      synopsisStr,
      synopsisTags,
    };
  }
  async activityContent(ctx: RequestContext, shoppingCreditsClaimActivity: ShoppingCreditsClaimActivity) {
    const ruleValues = shoppingCreditsClaimActivity.ruleValues;
    const ruleType = shoppingCreditsClaimActivity.ruleType;
    const unit = shoppingCreditsClaimActivity.type === ShoppingCreditConditionType.QuantityFullReduction ? '件' : '元'; // 购物金发放的单位，按件或者按元来计算
    const unitScale = shoppingCreditsClaimActivity.type === ShoppingCreditConditionType.QuantityFullReduction ? 1 : 100;
    if (ruleType === RuleType.Ladder) {
      const str = [];
      for (const ruleValue of ruleValues) {
        if (ruleValue.discountValue?.discountType === DiscountType.FixedAmount) {
          str.push(
            `满${ruleValue?.minimum / unitScale}${unit}可得购物金${
              Number(ruleValue?.discountValue?.discount ?? 0) / 100
            }`,
          );
        } else if (ruleValue.discountValue?.discountType === DiscountType.FixedPercent) {
          //买满50元,可返商品实付金额的10%购物金
          str.push(
            `买满${ruleValue?.minimum / unitScale}${unit},可返商品实付金额的${Number(
              ruleValue?.discountValue?.discount ?? 0,
            )}%购物金`,
          );
        }
      }
      return str;
    } else {
      const ruleValue = ruleValues[0];
      if (ruleValue.discountValue?.discountType === DiscountType.FixedAmount) {
        return [
          `每满${ruleValue?.minimum / unitScale}${unit}可得购物金${
            Number(ruleValue?.discountValue?.discount ?? 0) / 100
          },最高可得购物金${Number(ruleValue?.maximumOffer) / 100}`,
        ];
      } else if (ruleValue.discountValue?.discountType === DiscountType.FixedPercent) {
        //买满50元,可返商品实付金额的10%购物金
        return [
          `每满${ruleValue?.minimum / unitScale}${unit}可返商品实付金额的${Number(
            ruleValue?.discountValue?.discount ?? 0,
          )}%购物金,最高可得购物金${Number(ruleValue?.maximumOffer) / 100}`,
        ];
      }
      return [];
    }
  }

  async shoppingCreditClaimStateChangeAll() {
    const ctxs = await this.productCustomService.getAllCtxs();
    for (const ctx of ctxs) {
      await this.shoppingCreditClaimStateChange(ctx);
    }
  }
  async shoppingCreditClaimStateChange(ctx: RequestContext) {
    const now = new Date();
    const nowStarted = await this.connection
      .getRepository(ctx, ShoppingCreditsClaimActivity)
      .createQueryBuilder('activity')
      .leftJoinAndSelect('activity.channels', 'channel')
      .andWhere(`channel.id = :channelId`, {channelId: ctx.channelId})
      .andWhere(`activity.startTime <= :now`, {now})
      .andWhere(`activity.status = :status`, {status: ActivityStatus.NotStarted})
      .andWhere(`activity.deletedAt IS NULL`)
      .getMany();
    if (nowStarted.length > 0) {
      const notStartedIds = nowStarted.map(item => item.id);
      const notStartedPromotionIds = nowStarted.map(item => item.promotionId);
      if (notStartedIds && notStartedIds.length > 0) {
        await this.commonService.activeOrderTime(ctx, [ctx.channelId]);
        await this.connection
          .getRepository(ctx, ShoppingCreditsClaimActivity)
          .update({id: In(notStartedIds)}, {status: ActivityStatus.Normal});
        const notStartedKey = nowStarted.map(item => `Query:ShoppingCreditsClaimActivity:${item.id}:${ctx.channelId}`);
        await this.cacheService.removeCache(notStartedKey);
        // eslint-disable-next-line @typescript-eslint/no-misused-promises
        nowStarted.forEach(async item => {
          await this.productPromotionActiveService.activeProductCacheClear(ctx, item.applicableProduct);
        });
      }
      if (notStartedPromotionIds && notStartedPromotionIds.length > 0) {
        await this.connection.getRepository(ctx, Promotion).update({id: In(notStartedPromotionIds)}, {enabled: true});
        const notStartedPromotionKey = [
          ...notStartedPromotionIds.map(item => `Query:Promotion:${item}:${ctx.channelId}`),
          ...notStartedPromotionIds.map(item => `Query:Promotion:IncludeFailure:${item}:${ctx.channelId}`),
        ];
        await this.cacheService.removeCache(notStartedPromotionKey);
      }
    }

    const nowEnded = await this.connection
      .getRepository(ctx, ShoppingCreditsClaimActivity)
      .createQueryBuilder('activity')
      .leftJoinAndSelect('activity.channels', 'channel')
      .andWhere(`channel.id = :channelId`, {channelId: ctx.channelId})
      .andWhere(`activity.endTime <= :now`, {now})
      .andWhere(`activity.status = :status`, {status: ActivityStatus.Normal})
      .andWhere(`activity.deletedAt IS NULL`)
      .getMany();
    if (nowEnded.length > 0) {
      const endedIds = nowEnded.map(item => item.id);
      const endedPromotionIds = nowEnded.map(item => item.promotionId);
      if (endedIds && endedIds.length > 0) {
        await this.commonService.activeOrderTime(ctx, [ctx.channelId]);
        await this.connection
          .getRepository(ctx, ShoppingCreditsClaimActivity)
          .update({id: In(endedIds)}, {status: ActivityStatus.HaveEnded});
        const endedKey = nowEnded.map(item => `Query:ShoppingCreditsClaimActivity:${item.id}:${ctx.channelId}`);
        await this.cacheService.removeCache(endedKey);
        // eslint-disable-next-line @typescript-eslint/no-misused-promises
        nowEnded.forEach(async item => {
          await this.productPromotionActiveService.activeProductCacheClear(ctx, item.applicableProduct);
        });
      }
      if (endedPromotionIds && endedPromotionIds.length > 0) {
        await this.connection.getRepository(ctx, Promotion).update({id: In(endedPromotionIds)}, {enabled: false});
        const endedPromotionKey = [
          ...endedPromotionIds.map(item => `Query:Promotion:${item}:${ctx.channelId}`),
          ...endedPromotionIds.map(item => `Query:Promotion:IncludeFailure:${item}:${ctx.channelId}`),
        ];
        await this.cacheService.removeCache(endedPromotionKey);
      }
    }
  }
}
