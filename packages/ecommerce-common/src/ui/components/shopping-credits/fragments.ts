import gql from 'graphql-tag';

export const SHOPPING_CREDITS_CLAIM_FRAGMENT = gql`
  fragment ShoppingCreditsClaim on ShoppingCreditsClaimActivity {
    id
    createdAt
    updatedAt
    name
    remarks
    displayName
    status
    startTime
    endTime
    introduce
    type
    ruleType
    ruleValues {
      minimum
      discountValue {
        discountType
        discount
      }
      maximumOffer
    }
    applicableProduct {
      applicableType
      productIds
    }
    stackingDiscountSwitch
    stackingPromotionTypes
    smallProgramQRCodeLink
    promotionId
  }
`;
export const SHOPPING_CREDITS_DEDUCTION_FRAGMENT = gql`
  fragment ShoppingCreditsDeduction on ShoppingCreditsDeductionActivity {
    id
    createdAt
    updatedAt
    name
    remarks
    displayName
    status
    startTime
    endTime
    introduce
    minimum
    deductionRate
    applicableProduct {
      applicableType
      productIds
    }
    stackingDiscountSwitch
    stackingPromotionTypes
    smallProgramQRCodeLink
    promotionId
  }
`;
