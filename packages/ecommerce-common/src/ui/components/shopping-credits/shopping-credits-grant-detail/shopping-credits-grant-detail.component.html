<vdr-page-block>
  <vdr-action-bar>
    <vdr-ab-left></vdr-ab-left>
    <vdr-ab-right>
      <div *ngIf="isCopy || (isNew$ | async); else saveBtns">
        <button
          *vdrIfPermissions="['CreateShoppingCreditsClaimActivity']"
          class="btn btn-primary"
          (click)="save()"
          [disabled]="disabled"
        >
          创建
        </button>
        <button class="btn" [routerLink]="['../']">取消</button>
      </div>
      <ng-template #saveBtns>
        <div class="">
          <button
            *vdrIfPermissions="['UpdateShoppingCreditsClaimActivity']"
            class="btn btn-primary"
            (click)="save()"
            [disabled]="disabled"
          >
            保存
          </button>
          <button class="btn" [routerLink]="['../']">取消</button>
        </div>
      </ng-template>
    </vdr-ab-right>
  </vdr-action-bar>

  <div class="card">
    <div class="card-header">基本信息</div>
    <div class="card-block">
      <form class="form" [formGroup]="detailForm">
        <app-form-field label="活动页名称（显示在小程序扫码页面的顶部，最多输入10个字）">
          <input type="text" class="input-w100" formControlName="name" [maxlength]="10" />
        </app-form-field>
        <app-form-field label="商品详情标签（显示在参与该活动的商品详情中，最多输入10个字）">
          <input type="text" class="input-w100" formControlName="displayName" [maxlength]="10" />
        </app-form-field>
        <app-form-field label="名称备注（仅限内部人员查阅，最多输入30个字）">
          <input id="name" class="input-w100" type="text" formControlName="remarks" [maxLength]="30" />
        </app-form-field>
        <app-form-field label="活动时间">
          <date-range [isActivityRange]="true" formControlName="dateRange"></date-range>
        </app-form-field>
      </form>
    </div>
  </div>

  <div class="card">
    <div class="card-header">活动规则</div>
    <div class="card-block">
      <form class="form" [formGroup]="detailForm">
        <app-form-field label="活动类型">
          <div class="clr-control-container clr-control-inline">
            <app-radio label="满额" value="amountFullReduction" formControlName="type"></app-radio>
            <app-radio label="满件" value="quantityFullReduction" formControlName="type"></app-radio>
          </div>
        </app-form-field>
        <app-form-field label="优惠规则">
          <div class="clr-control-container clr-control-inline">
            <app-radio
              [label]="isAmountMinimum ? '阶梯优惠（例：满20元返10元购物金）' : '阶梯优惠（例：满2件返10元购物金）'"
              value="ladder"
              formControlName="ruleType"
            ></app-radio>
            <app-radio
              [label]="isAmountMinimum ? '循环优惠（例：每满20元返10元购物金）' : '循环优惠（例：每满2件返10元购物金）'"
              value="cycle"
              formControlName="ruleType"
            ></app-radio>
          </div>
        </app-form-field>
        <div class="variants-preview">
          <table *ngIf="ruleValuesFormArr?.controls?.length" class="table">
            <thead>
              <tr>
                <th>序号</th>
                <th class="left">{{ isAmountMinimum ? '门槛（金额需正序递增）' : '门槛（需正序递增）' }}</th>
                <th class="left">
                  {{ isAmountMinimum ? '返还购物金（需正序递增，且不大于门槛金额）' : '返还购物金（需正序递增）' }}
                </th>
                <th class="left" *ngIf="!isLadder">返还上限</th>
                <th>操作</th>
              </tr>
            </thead>
            <!-- <ng-container *ngFor="let rule of ruleList; let i = index"></ng-container> -->
            <tr *ngFor="let rule of ruleValuesFormArr.controls; let index = index" [formGroup]="rule">
              <td class="align-middle">{{ index + 1 }}</td>
              <td class="align-middle no-wrap">
                <div class="flex items-center nowrap">
                  满
                  <ng-container *ngIf="isAmountMinimum; else quantityMinimum">
                    <price-input style="max-width: 8em" formControlName="minimum"></price-input>
                  </ng-container>
                  <ng-template #quantityMinimum>
                    <input type="number" formControlName="minimum" [min]="1" appInteger />件
                  </ng-template>
                </div>
              </td>
              <td class="align-middle">
                <div class="flex items-center nowrap" formGroupName="discountValue">
                  <ng-select
                    [items]="discountTypeOptions"
                    [clearable]="false"
                    bindLabel="label"
                    bindValue="value"
                    formControlName="discountType"
                    (change)="handleDiscountTypeChange(rule)"
                  ></ng-select>
                  返回
                  <ng-container *ngIf="rule.get(['discountValue', 'discountType']) as discountType">
                    <price-input
                      [multiply]="discountType.value === 'fixedAmount' ? 100 : 1"
                      [convertFix]="discountType.value === 'fixedAmount' ? 2 : 0"
                      [unit]="discountType.value === 'fixedAmount' ? '元' : '%'"
                      [max]="discountType.value === 'fixedAmount' ? null : 100"
                      formControlName="discount"
                      (input)="handleInputDiscount($event, rule)"
                    ></price-input>
                  </ng-container>
                </div>
              </td>
              <td *ngIf="!isLadder" class="align-middle">
                <div class="flex items-center nowrap">
                  <price-input
                    style="max-width: 8em"
                    formControlName="maximumOffer"
                    (input)="handleInputDiscount($event, rule)"
                  ></price-input>
                </div>
              </td>
              <td class="align-middle">
                <app-button-delete *ngIf="ruleValuesFormArr?.length > 1" (click)="rmRule(index)"></app-button-delete>
              </td>
            </tr>
          </table>
          <button
            *ngIf="!(!isLadder && ruleValuesFormArr?.length)"
            [disabled]="ruleList.length >= 5"
            [class.btn-danger]="!ruleValuesFormArr?.length"
            class="btn btn-primary"
            (click)="addRule()"
          >
            <clr-icon shape="plus"></clr-icon>
            添加优惠
          </button>
        </div>
        <app-form-field label="活动商品">
          <div formGroupName="applicableProduct">
            <div class="clr-control-container clr-control-inline">
              <app-radio label="全部商品可用" value="all" formControlName="applicableType"></app-radio>
              <app-radio label="指定商品可用" value="availableGoods" formControlName="applicableType"></app-radio>
              <app-radio label="指定商品不可用" value="unusableGoods" formControlName="applicableType"></app-radio>
            </div>
            <app-activity-product-selector
              *ngIf="detailForm.controls?.applicableProduct?.value.applicableType !== 'all'"
              [startAt]="detailForm.controls?.dateRange?.value?.startTime"
              [endAt]="detailForm.controls?.dateRange?.value?.endTime"
              [applicableProducts]="detailForm.controls?.applicableProduct?.value.productIds"
              [disableVirtualTypes]="['coupon']"
              formControlName="productIds"
              promotionType="shoppingCreditsClaim"
            >
            </app-activity-product-selector>
          </div>
        </app-form-field>
        <app-form-field label="优惠叠加">
          <div class="clr-control-container clr-control-inline">
            <app-radio label="不叠加其它营销活动" [value]="false" formControlName="stackingDiscountSwitch"></app-radio>
            <app-radio label="叠加其它营销活动" [value]="true" formControlName="stackingDiscountSwitch"></app-radio>
          </div>
        </app-form-field>
        <app-form-field label="可叠加活动" *ngIf="detailForm.value.stackingDiscountSwitch">
          <app-checkbox [options]="options" formControlName="stackingPromotionTypes"></app-checkbox>
        </app-form-field>
        <app-form-field label="规则说明">
          <textarea formControlName="introduce" cols="30" rows="10"></textarea>
        </app-form-field>
      </form>
    </div>
  </div>
</vdr-page-block>
