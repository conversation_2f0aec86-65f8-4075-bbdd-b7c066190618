/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Component,
  OnInit,
  // ChangeDetectorRef,
} from '@angular/core';
import {FormBuilder, FormGroup, UntypedFormGroup, Validators} from '@angular/forms';
import {ActivatedRoute, Router} from '@angular/router';
import {
  BaseDetailComponent,
  ServerConfigService,
  DataService,
  NotificationService,
  // ModalService,
} from '@vendure/admin-ui/core';
import {LanguageCode} from '@vendure/admin-ui/core';
import {UPSERT_SHOPPING_CREDITS_DEDUCTION} from '../graphql';
import {dateRangeRequiredValidator, valueValidatorChangeByTrigger} from '../../../utils/validators';

@Component({
  selector: 'app-shopping-credits-use-detail',
  templateUrl: './shopping-credits-use-detail.component.html',
  styleUrls: ['./shopping-credits-use-detail.component.scss'],
})
export class ShoppingCreditsUseDetailComponent extends BaseDetailComponent<any> implements OnInit {
  detailForm: UntypedFormGroup;

  isCopy = false;
  applicableProductValidator: any;

  options = [
    {
      value: 'packageDiscount',
      label: '打包一口价',
    },
    {
      value: 'member',
      label: '会员折扣',
    },
    {
      value: 'discountActivity',
      label: '第x件x折',
    },
    {
      value: 'fullDiscountPresent',
      label: '满减满赠',
    },
    {
      value: 'purchaseAtAPremium',
      label: '加价购',
    },
    {
      value: 'coupon',
      label: '优惠券',
    },
    {
      value: 'actuallyPaid',
      label: '实付满赠',
    },
    {
      value: 'selectiveGift',
      label: '任选满赠',
    },
    {
      value: 'paymentReward',
      label: '支付有礼',
    },
    {
      value: 'shoppingCreditsClaim',
      label: '购物金发放',
    },
  ];

  get disabled() {
    return this.detailForm?.invalid || this.detailForm?.pristine;
  }

  get applicableProductForm() {
    return this.detailForm?.controls?.applicableProduct as FormGroup;
  }

  constructor(
    route: ActivatedRoute,
    router: Router,
    serverCfg: ServerConfigService,
    protected dataService: DataService,
    private fb: FormBuilder,
    private notification: NotificationService,
  ) {
    super(route, router, serverCfg, dataService);
    this.detailForm = this.fb.group({
      name: ['', Validators.required],
      displayName: ['', Validators.required],
      remarks: ['', Validators.required],
      dateRange: [undefined, dateRangeRequiredValidator()],
      introduce: [''],
      minimum: [undefined, Validators.required],
      deductionRate: [undefined, Validators.required],
      applicableProduct: this.fb.group({
        applicableType: ['availableGoods', Validators.required],
        productIds: undefined,
      }),
      stackingDiscountSwitch: [true],
      stackingPromotionTypes: [[]],
    });

    this.applicableProductValidator = valueValidatorChangeByTrigger(
      this.applicableProductForm,
      'applicableType',
      'productIds',
      (type: string) => type && type !== 'all',
      ids => !ids?.length,
    );
    this.applicableProductForm.controls.productIds.setValidators(this.applicableProductValidator.validatorFn);
    this.applicableProductForm.controls.productIds.updateValueAndValidity();

    this.detailForm.controls.stackingPromotionTypes.setValidators(
      valueValidatorChangeByTrigger(this.detailForm, 'stackingDiscountSwitch', 'stackingPromotionTypes').validatorFn,
    );
    this.detailForm.controls.stackingPromotionTypes.updateValueAndValidity();
  }

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.isCopy = params['copy'];
    });
    this.applicableProductValidator.subs();
    valueValidatorChangeByTrigger(this.detailForm, 'stackingDiscountSwitch', 'stackingPromotionTypes').subs();
    this.init();
  }

  protected setFormValues(entity: any, languageCode: LanguageCode): void {
    console.log(entity);
    if (!entity?.id) {
      return;
    }
    const {
      name,
      displayName,
      remarks,
      startTime,
      endTime,
      introduce,
      minimum,
      deductionRate,
      applicableProduct,
      stackingDiscountSwitch,
      stackingPromotionTypes,
    } = entity;
    this.detailForm.patchValue({
      name,
      displayName,
      remarks,
      dateRange: {
        startTime,
        endTime,
      },
      minimum,
      introduce,
      deductionRate,
      applicableProduct,
      stackingDiscountSwitch,
      stackingPromotionTypes,
    });
    setTimeout(() => {
      this.detailForm.markAsPristine();
    });
  }

  save() {
    console.log(this.detailForm.getRawValue());
    const {dateRange, ...rest} = this.detailForm.getRawValue();
    const input = {
      id: this.isCopy || !this.id ? undefined : this.id,
      ...rest,
      startTime: dateRange.startTime,
      endTime: dateRange.endTime,
    };
    this.dataService.mutate(UPSERT_SHOPPING_CREDITS_DEDUCTION, {input}).subscribe(res => {
      this.notification.success('操作成功');
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      this.router.navigate(['../'], {relativeTo: this.route});
    });
  }
}
