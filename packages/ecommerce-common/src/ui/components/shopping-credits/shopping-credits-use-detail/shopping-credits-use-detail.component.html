<vdr-page-block>
  <vdr-action-bar>
    <vdr-ab-left></vdr-ab-left>
    <vdr-ab-right>
      <div class="" *ngIf="isCopy || isNew$ | async; else saveBtns">
        <button
          *vdrIfPermissions="['CreateShoppingCreditsDeductionActivity']"
          class="btn btn-primary"
          (click)="save()"
          [disabled]="disabled"
        >
          创建
        </button>
        <button class="btn" [routerLink]="['../']">取消</button>
      </div>
      <ng-template #saveBtns>
        <div class="">
          <button
            *vdrIfPermissions="['UpdateShoppingCreditsDeductionActivity']"
            class="btn btn-primary"
            (click)="save()"
            [disabled]="disabled"
          >
            保存
          </button>
          <button class="btn" [routerLink]="['../']">取消</button>
        </div>
      </ng-template>
    </vdr-ab-right>
  </vdr-action-bar>

  <div class="card">
    <div class="card-header">基本信息</div>
    <div class="card-block">
      <form class="form" [formGroup]="detailForm">
        <app-form-field label="活动页名称（显示在小程序扫码页面的顶部，最多输入10个字）">
          <input type="text" class="input-w100" formControlName="name" [maxlength]="10" />
        </app-form-field>
        <app-form-field label="商品详情标签（显示在参与该活动的商品详情中，最多输入10个字）">
          <input type="text" class="input-w100" formControlName="displayName" [maxlength]="10" />
        </app-form-field>
        <app-form-field label="名称备注（仅限内部人员查阅，最多输入30个字）">
          <input id="name" class="input-w100" type="text" formControlName="remarks" [maxLength]="30" />
        </app-form-field>
        <app-form-field label="活动时间">
          <date-range [isActivityRange]="true" formControlName="dateRange"></date-range>
        </app-form-field>
      </form>
    </div>
  </div>

  <div class="card">
    <div class="card-header">活动规则</div>
    <div class="card-block">
      <form class="form" [formGroup]="detailForm">
        <app-form-field label="购物金使用门槛">
          <price-input [min]="0" style="width: 175px" formControlName="minimum"></price-input>
          <!-- <input type="number" [max]="99.9" [min]="0.01" formControlName="deductionRate" /> -->
        </app-form-field>
        <app-form-field
          label="购物金抵扣比例（按可用商品金额的可使用比例使用：如金额100元，可使用比例10%，则可使用购物金抵扣10元）"
        >
          <price-input
            [multiply]="1"
            [convertFix]="0"
            [max]="100"
            [min]="0"
            unit="%"
            style="width: 175px"
            formControlName="deductionRate"
            appInteger
          ></price-input>
          <!-- <input type="number" [max]="99.9" [min]="0.01" formControlName="deductionRate" /> -->
        </app-form-field>
        <app-form-field label="活动商品">
          <div formGroupName="applicableProduct">
            <div class="clr-control-container clr-control-inline">
              <app-radio label="全部商品可用" value="all" formControlName="applicableType"></app-radio>
              <app-radio label="指定商品可用" value="availableGoods" formControlName="applicableType"></app-radio>
              <app-radio label="指定商品不可用" value="unusableGoods" formControlName="applicableType"></app-radio>
            </div>
            <app-activity-product-selector
              *ngIf="detailForm.controls?.applicableProduct?.value.applicableType !== 'all'"
              [startAt]="detailForm.controls?.dateRange?.value?.startTime"
              [endAt]="detailForm.controls?.dateRange?.value?.endTime"
              [applicableProducts]="detailForm.controls?.applicableProduct?.value.productIds"
              [disableVirtualTypes]="['coupon']"
              formControlName="productIds"
              promotionType="shoppingCreditsDeduction"
            >
            </app-activity-product-selector>
          </div>
        </app-form-field>
        <app-form-field label="优惠叠加">
          <div class="clr-control-container clr-control-inline">
            <app-radio label="不叠加其它营销活动" [value]="false" formControlName="stackingDiscountSwitch"></app-radio>
            <app-radio label="叠加其它营销活动" [value]="true" formControlName="stackingDiscountSwitch"></app-radio>
          </div>
        </app-form-field>
        <app-form-field label="可叠加活动" *ngIf="detailForm.value.stackingDiscountSwitch">
          <app-checkbox [options]="options" formControlName="stackingPromotionTypes"></app-checkbox>
        </app-form-field>
        <app-form-field label="规则说明">
          <textarea formControlName="introduce" cols="30" rows="10"></textarea>
        </app-form-field>
      </form>
    </div>
  </div>
</vdr-page-block>
