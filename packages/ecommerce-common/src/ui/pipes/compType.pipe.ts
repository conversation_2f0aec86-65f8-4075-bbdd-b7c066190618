import {Pipe, PipeTransform} from '@angular/core';

@Pipe({
  name: 'compType',
})
export class CompTypePipe implements PipeTransform {
  transform(value: string): string {
    return (
      {
        checkin: '签到组件',
        picture: '图片',
        banner: 'Banner',
        product: '商品分组',
        video: '视频',
        productItems: '商品卡片',
        category: '自定义卡片',
        countdown: '倒计时组件',
        shoppingCredits: '购物金组件',
      }[value] || value
    );
  }
}
