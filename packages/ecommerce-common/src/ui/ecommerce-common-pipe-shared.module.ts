import {NgModule} from '@angular/core';

import {SharedModule} from '@vendure/admin-ui/core';

import {TokenCNYPipe} from './pipes/tokenCNY.pipe';
import {OrderStatePipe} from './pipes/orderState.pipe';
import {StringToColorPipe} from './pipes/string-to-color.pipe';
import {DeliverState} from './pipes/deliverState.pipe';
import {AfterSaleTranslatePipe} from './pipes/afterSaleTranslate.pipe';
import {FmDatePipe} from './pipes/fmDate.pipe';
import {StatePipe} from './pipes/state.pipe';
import {IsNotPipe} from './pipes/isNot.pipe';
import {PlanStateTranslatePipe} from './pipes/planState.pipe';
import {JumpTYpeTranslatePipe} from './pipes/jumpType.pipe';
import {FmPercentPipe} from './pipes/fmPercent.pipe';
import {CouponStatusPipe} from './pipes/couponStatus.pipe';
import {CouponStatePipe} from './pipes/couponState.pipe';
import {CouponTypePipe} from './pipes/couponType.pipe';
import {ExportStatusPipe} from './pipes/exportStatus.pipe';

const PIPES = [
  DeliverState,
  AfterSaleTranslatePipe,
  TokenCNYPipe,
  OrderStatePipe,
  StringToColorPipe,
  FmDatePipe,
  StatePipe,
  IsNotPipe,
  PlanStateTranslatePipe,
  JumpTYpeTranslatePipe,
  FmPercentPipe,
  CouponStatusPipe,
  CouponStatePipe,
  CouponTypePipe,
  ExportStatusPipe,
];

@NgModule({
  imports: [SharedModule],
  exports: [...PIPES],
  declarations: [...PIPES],
})
export class EcommerceCommonPipeSharedModule {}
