import {NgModule, APP_INITIALIZER} from '@angular/core';
// import {StyleService} from '@vendure/admin-ui/core';
// import {SubscriptionListComponent} from './components/subscription-list/subscription-list.component';
import {
  SharedModule,
  addNavMenuSection,
  addNavMenuItem,
  // registerCustomDetailComponent,
  registerFormInputComponent,
  registerDashboardWidget,
} from '@vendure/admin-ui/core';
// import {ProductImgsComponent} from './components/product-imgs/product-imgs.component';
import {ProductImgsModule} from './components/product-imgs/product-imgs.module';
// import {ParticularsComponent} from './customFields/particulars/particulars.component';
import {ParticularsModule} from './customFields/particulars/particulars.module';
// import {ProductImgsComponent} from './components/product-imgs/product-imgs.component';
import {ParticularsComponent} from './customFields/particulars/particulars.component';
import {StyleLoaderService} from './style-loader.service';
import {TrimDirective} from './directives/trim.directive';
import {AutoWidthDirective} from './directives/input/auto-width.directive';
import {registerDefaultFormInputs} from './components/dynamic-form-input/register-dynamic-input-components';
import {AppFileInputModule} from './components/forms/app-file-input/app-file-input.module';
import {AppFileInputComponent} from './components/forms/app-file-input/app-file-input.component';
import {OrderChartWidgetComponent} from './components/dashboard-widgets/order-chart-widget/order-chart-widget.component';
import {OrderChartWidgetModule} from './components/dashboard-widgets/order-chart-widget/order-chart-widget.module';
import {OrderSummaryWidgetComponent} from './components/dashboard-widgets/order-summary-widget/order-summary-widget.component';
import {OrderSummaryWidgetModule} from './components/dashboard-widgets/order-summary-widget/order-summary-widget.component';

const DIRECTIVES = [TrimDirective, AutoWidthDirective];
// @Injectable({
//   providedIn: 'root',
// })
@NgModule({
  imports: [
    SharedModule,
    ProductImgsModule,
    ParticularsModule,
    AppFileInputModule,
    OrderChartWidgetModule,
    OrderSummaryWidgetModule,
  ],
  declarations: [...DIRECTIVES],
  exports: [...DIRECTIVES],
  providers: [
    {
      provide: APP_INITIALIZER,
      useFactory: (styleLoader: StyleLoaderService) => () => styleLoader.loadStyles(),
      deps: [StyleLoaderService],
      multi: true,
    },
    registerDashboardWidget('metrics', {
      title: '订单数据看板',
      loadComponent: () => OrderChartWidgetComponent,
      requiresPermissions: ['ReadOrder'],
    }),
    registerDashboardWidget('orderSummary', {
      title: '订单预览',
      loadComponent: () => OrderSummaryWidgetComponent,
      requiresPermissions: ['ReadOrder'],
    }),
    registerDefaultFormInputs(),
    registerFormInputComponent('particulars-select', ParticularsComponent),
    registerFormInputComponent('app-file-input', AppFileInputComponent),
    // registerCustomDetailComponent({
    //   locationId: 'product-detail',
    //   component: ProductImgsComponent,
    // }),
    addNavMenuSection(
      {
        id: 'catalog',
        label: '商品管理',
        requiresPermission: userPermissions => {
          // console.log(userPermissions);
          return ['ReadProduct', 'ReadFacet', 'ReadCollection', 'ReadAsset'].some(item =>
            userPermissions.includes(item),
          );
        },
        items: [
          {
            id: 'inventory',
            label: '商品列表',
            routerLink: ['/extensions/ecommerce/product-list'],
            icon: 'library',
            requiresPermission: 'ReadProduct',
          },
          {
            id: 'facets',
            label: '商品特征',
            routerLink: ['/extensions/ecommerce/facet-list'],
            icon: 'tag',
            requiresPermission: 'ReadFacet',
          },
          {
            id: 'collections',
            label: '商品系列',
            routerLink: ['/extensions/ecommerce/collection-list'],
            icon: 'folder-open',
            requiresPermission: 'ReadCollection',
          },
          {
            id: 'assets',
            label: '资源列表',
            routerLink: ['/extensions/ecommerce/asset-list'],
            icon: 'image-gallery',
            requiresPermission: 'ReadAsset',
          },
        ],
      },
      'catalog',
    ),
    addNavMenuSection({
      id: 'sales',
      label: '销售管理',
      requiresPermission: userPermissions => {
        return ['ReadOrder', 'AfterSaleOperate', 'ReadAfterSaleChannelAddress'].some(item =>
          userPermissions.includes(item),
        );
      },
      items: [
        {
          id: 'orders',
          label: '订单列表',
          routerLink: ['/extensions/ecommerce/order-list'],
          icon: 'shopping-cart',
          requiresPermission: 'ReadOrder',
        },
        {
          id: 'after-sale-list',
          label: '售后列表',
          routerLink: ['/extensions/ecommerce/after-sale-list'],
          icon: 'calendar',
          requiresPermission: 'AfterSaleOperate',
        },
        {
          id: 'channel-address',
          label: '退货地址列表',
          routerLink: ['/extensions/ecommerce/channel-address-list'],
          icon: 'calendar',
          requiresPermission: 'ReadAfterSaleChannelAddress',
        },
      ],
    }),

    // addNavMenuItem(
    //   {
    //     id: 'orders',
    //     label: '订单列表',
    //     routerLink: ['/extensions/ecommerce/order-list'],
    //     icon: 'shopping-cart',
    //   },
    //   'sales',
    // ),
    addNavMenuSection({
      id: 'customers',
      label: '客户管理',
      requiresPermission: userPermissions => {
        return ['GroupCustomerOperate', 'ReadCustomer'].some(item => userPermissions.includes(item));
      },
      items: [
        {
          requiresPermission: 'ReadCustomer',
          id: 'customers',
          label: '客户列表',
          routerLink: ['/extensions/ecommerce/customers-list'],
          icon: 'user',
        },
        {
          requiresPermission: 'GroupCustomerOperate',
          id: 'division',
          label: '人群列表',
          routerLink: ['/extensions/ecommerce/customer-division-list'],
          icon: 'user',
        },
      ],
    }),
    // addNavMenuItem(
    //   {
    //     id: 'customers',
    //     label: '客户管理',
    //     routerLink: ['/extensions/ecommerce/customers-list'],
    //     icon: 'user',
    //   },
    //   'customers',
    // ),

    // addNavMenuItem(
    //   {
    //     id: 'customer-groups',
    //     label: '分组管理',
    //     routerLink: ['/extensions/ecommerce/customer-group-list'],
    //     icon: 'user',
    //   },
    //   'customers',
    // ),
    addNavMenuSection(
      {
        id: 'shop',
        label: '店铺管理',
        requiresPermission: userPermissions => {
          // console.log(userPermissions);
          return ['CustomPageOperate', 'HotWordOperate', 'AnnouncementOperate', 'SettingOperate'].some(item =>
            userPermissions.includes(item),
          );
        },
        items: [
          {
            id: 'custom-page-list',
            label: '自定义页面',
            routerLink: ['/extensions/ecommerce/custom-page-list'],
            icon: 'file-group',
            requiresPermission: 'CustomPageOperate',
          },
          {
            id: 'search-hot-words',
            label: '搜索热词管理',
            routerLink: ['/extensions/ecommerce/search-hot-words'],
            icon: 'text',
            requiresPermission: 'HotWordOperate',
          },
          {
            id: 'announcement',
            label: '公告管理',
            routerLink: ['/extensions/ecommerce/announcement'],
            icon: 'calendar',
            requiresPermission: 'AnnouncementOperate',
          },
          {
            id: 'recommendManagement',
            label: '推荐管理',
            routerLink: ['/extensions/ecommerce/recommend-management'],
            icon: 'tags',
            requiresPermission: 'SettingOperate',
          },
          {
            id: 'share-settings',
            label: '分享设置管理',
            routerLink: ['/extensions/ecommerce/share-settings'],
            icon: 'tags',
            requiresPermission: 'ReadShareSetting',
          },
          {
            id: 'personal-center-settings',
            label: '个人中心配置',
            routerLink: ['/extensions/ecommerce/personal-center-settings'],
            icon: 'tags',
            requiresPermission: 'ReadPersonalCenter',
          },
          {
            id: 'shopping-credits-config',
            label: '购物金设置管理',
            routerLink: ['/extensions/ecommerce/shopping-credits-config'],
            icon: 'tags',
            requiresPermission: 'ReadShoppingCreditsConfig',
          },
        ],
      },
      'marketing',
    ),
    addNavMenuSection(
      {
        id: 'shop-notes',
        label: '心愿笔记',
        requiresPermission: userPermissions => {
          return ['ReadForumTag', 'ReadForumActivity', 'ReadForumPost', 'ReadForumReview', 'ReadForumCustomer'].some(
            item => userPermissions.includes(item),
          );
        },
        items: [
          {
            id: 'topic-management-list',
            label: '话题管理',
            routerLink: ['/extensions/ecommerce/topic-management-list'],
            icon: 'file-group',
            requiresPermission: 'ReadForumTag',
          },
          {
            id: 'note-activity-list',
            label: '活动管理',
            routerLink: ['/extensions/ecommerce/note-activity-list'],
            icon: 'text',
            requiresPermission: 'ReadForumActivity',
          },
          {
            id: 'customer-post-list',
            label: '用户帖子管理',
            routerLink: ['/extensions/ecommerce/customer-post-list'],
            icon: 'calendar',
            requiresPermission: 'ReadForumPost',
          },
          {
            id: 'channel-post-list',
            label: '平台帖子管理',
            routerLink: ['/extensions/ecommerce/channel-post-list'],
            icon: 'tags',
            requiresPermission: 'ReadForumPost',
          },
          {
            id: 'post-comment-list',
            label: '帖子评论管理',
            routerLink: ['/extensions/ecommerce/post-comment-list'],
            icon: 'tags',
            requiresPermission: 'ReadForumReview',
          },
          {
            id: 'forum-customer-list',
            label: '发文账号管理',
            routerLink: ['/extensions/ecommerce/forum-customer-list'],
            icon: 'tags',
            requiresPermission: 'ReadForumCustomer',
          },
        ],
      },
      'marketing',
    ),
    addNavMenuSection(
      {
        id: 'member-card',
        label: '会员卡管理',
        requiresPermission: userPermissions => {
          return [
            'MembershipPlanOperate',
            'ReadMembershipPlan',
            'MemberClaimRecordRead',
            'MemberReturnCardRecordRead',
          ].some(item => userPermissions.includes(item));
        },
        items: [
          {
            id: 'member-card-list',
            label: '会员卡列表',
            routerLink: ['/extensions/ecommerce/member-card-list'],
            icon: 'credit-card',
          },
          {
            id: 'member-card-get-record',
            label: '领卡记录',
            routerLink: ['/extensions/ecommerce/member-card-get-record-list'],
            icon: 'expand-card',
            requiresPermission: 'MemberClaimRecordRead',
          },
          {
            id: 'member-card-refund-record',
            label: '退卡记录',
            routerLink: ['/extensions/ecommerce/member-card-refund-record-list'],
            icon: 'collapse-card',
            requiresPermission: 'MemberReturnCardRecordRead',
          },
        ],
      },
      'marketing',
    ),
    addNavMenuSection(
      {
        id: 'gift-card',
        label: '礼品卡管理',
        requiresPermission: userPermissions => {
          return ['ReadGiftCard', 'ReadGiftCardOrder'].some(item => userPermissions.includes(item));
        },
        items: [
          {
            id: 'gift-card-list',
            label: '礼品卡列表',
            routerLink: ['/extensions/ecommerce/gift-card-list'],
            icon: 'credit-card',
            requiresPermission: 'ReadGiftCard',
          },
          {
            id: 'gift-card-get-list',
            label: '领卡记录',
            routerLink: ['/extensions/ecommerce/gift-card-get-list'],
            icon: 'expand-card',
            requiresPermission: 'ReadGiftCardOrder',
          },
          {
            id: 'gift-card-refund-record',
            label: '退卡记录',
            routerLink: ['/extensions/ecommerce/gift-card-return-list'],
            icon: 'collapse-card',
            requiresPermission: 'ReadGiftCardOrder',
          },
        ],
      },
      'marketing',
    ),
    addNavMenuSection(
      {
        id: 'points-shop',
        label: '积分商城',
        requiresPermission: userPermissions => {
          return ['ReadPointsProduct', 'ReadPointsConfig'].some(item => userPermissions.includes(item));
        },
        items: [
          {
            id: 'points-shop-list',
            label: '积分商品管理',
            routerLink: ['/extensions/ecommerce/points-shop-list'],
            icon: 'file-group',
            requiresPermission: 'ReadPointsProduct',
          },
          {
            id: 'points-shop-rule',
            label: '积分规则管理',
            routerLink: ['/extensions/ecommerce/points-shop-rule'],
            icon: 'text',
            requiresPermission: 'ReadPointsConfig',
          },
        ],
      },
      'marketing',
    ),
    addNavMenuSection(
      {
        id: 'check-in-management',
        label: '签到管理',
        requiresPermission: userPermissions => {
          return ['ReadPointsProduct', 'ReadPointsConfig'].some(item => userPermissions.includes(item));
        },
        items: [
          // {
          //   id: 'check-in-statistics',
          //   label: '签到数据统计',
          //   routerLink: ['/extensions/ecommerce/check-in-statistics'],
          //   icon: 'file-group',
          //   requiresPermission: 'ReadPointsProduct',
          // },
          {
            id: 'check-in-config',
            label: '签到规则设置',
            routerLink: ['/extensions/ecommerce/check-in-config'],
            icon: 'text',
            requiresPermission: 'ReadPointsConfig',
          },
        ],
      },
      'marketing',
    ),
    addNavMenuSection(
      {
        id: 'blind-box',
        label: '盲盒管理',
        requiresPermission: userPermissions => {
          return [
            'ReadAssistGift',
            'ReadBlindBoxActivityLimitConfig',
            'ReadBlindBoxActivity',
            'ReadBlindBoxBuy',
            'ReadBlindBox',
            'BlindBoxStatisticsOperate',
          ].some(item => userPermissions.includes(item));
        },
        items: [
          {
            id: 'blind-box-statistics',
            label: '盲盒数据统计',
            routerLink: ['/extensions/ecommerce/blind-box-statistics'],
            icon: 'file-group',
            requiresPermission: 'BlindBoxStatisticsOperate',
          },
          {
            id: 'blind-box-config',
            label: '盲盒通用设置',
            routerLink: ['/extensions/ecommerce/blind-box-config'],
            icon: 'text',
            requiresPermission: userPermissions => {
              return ['ReadAssistGift', 'ReadBlindBoxActivityLimitConfig'].some(item => userPermissions.includes(item));
            },
          },
          {
            id: 'blind-box-list',
            label: '盲盒列表',
            routerLink: ['/extensions/ecommerce/blind-box-list'],
            icon: 'text',
            requiresPermission: 'ReadBlindBoxActivity',
          },
          {
            id: 'blind-box-activity-list',
            label: '盲盒活动列表',
            routerLink: ['/extensions/ecommerce/blind-box-activity-list'],
            icon: 'text',
            requiresPermission: 'ReadBlindBoxActivity',
          },
          {
            id: 'blind-box-open-record-list',
            label: '开盒记录列表',
            routerLink: ['/extensions/ecommerce/blind-box-open-record-list'],
            icon: 'text',
            requiresPermission: 'ReadBlindBoxBuy',
          },
        ],
      },
      'marketing',
    ),
    addNavMenuSection({
      id: 'marketing',
      label: '营销管理',
      requiresPermission: userPermissions => {
        return [
          'CouponOperate',
          'ReadCoupon',
          'PurchasePremiumOperate',
          'DistributorOperate',
          'ReadDistributor',
          'DiscountActivityOperate',
          'FullDiscountPresentOperate',
          'FreeGiftOperate',
          'PackageDiscountOperate',
          'OperationPlanOperate',
          'ReadMemberPrice',
          'ReadSelectiveGiftActivity',
        ].some(item => userPermissions.includes(item));
      },
      items: [
        {
          id: 'operation-plan-list',
          label: '运营计划列表',
          routerLink: ['/extensions/ecommerce/operation-plan-list'],
          icon: 'credit-card',
          requiresPermission: 'OperationPlanOperate',
        },
        {
          id: 'coupons-list',
          label: '优惠券列表',
          routerLink: ['/extensions/ecommerce/coupons-list'],
          icon: 'scroll',
          requiresPermission: 'CouponOperate',
        },
        {
          id: 'coupon-package',
          label: '优惠券礼包',
          routerLink: ['/extensions/ecommerce/coupon-package'],
          icon: 'scroll',
          requiresPermission: 'ReadCouponBundle',
        },
        {
          id: 'new-customer-package',
          label: '新客授权礼包',
          routerLink: ['/extensions/ecommerce/new-customer-package'],
          icon: 'scroll',
          requiresPermission: 'ReadFirstCustomerBenefit',
        },
        {
          id: 'mark-up-buy-list',
          label: '加价购列表',
          routerLink: ['/extensions/ecommerce/mark-up-buy-list'],
          icon: 'add-text',
          requiresPermission: 'PurchasePremiumOperate',
        },
        {
          id: 'distributor-list',
          label: '分销管理',
          routerLink: ['/extensions/ecommerce/distribution/distributor/list'],
          icon: 'calendar',
          requiresPermission: 'ReadDistributor',
        },
        {
          id: 'distributor-group-list',
          label: '分销组列表',
          routerLink: ['/extensions/ecommerce/distribution/distributor-group/list'],
          icon: 'calendar',
          requiresPermission: 'ReadDistributor',
        },
        {
          id: 'half-price-list',
          label: '第x件x折活动',
          routerLink: ['/extensions/ecommerce/half-price-list'],
          icon: 'number-list',
          requiresPermission: 'DiscountActivityOperate',
        },
        {
          id: 'gift-list',
          label: '赠品管理',
          routerLink: ['/extensions/ecommerce/gift-list'],
          icon: 'sun',
          requiresPermission: 'FreeGiftOperate',
        },
        {
          id: 'full-gift-list',
          label: '满减满赠',
          routerLink: ['/extensions/ecommerce/full-gift-list'],
          icon: 'volume',
          requiresPermission: 'FullDiscountPresentOperate',
        },
        {
          id: 'package-discount-list',
          label: '打包一口价',
          routerLink: ['/extensions/ecommerce/package-discount-list'],
          icon: 'volume',
          requiresPermission: 'PackageDiscountOperate',
        },
        {
          id: 'member-price-list',
          label: '会员价活动',
          routerLink: ['/extensions/ecommerce/member-price-list'],
          icon: 'volume',
          requiresPermission: 'ReadMemberPrice',
        },
        {
          id: 'optional-discount',
          label: '任选满赠',
          routerLink: ['/extensions/ecommerce/optional-discount-list'],
          requiresPermission: 'ReadSelectiveGiftActivity',
        },
        {
          id: 'pay-gift',
          label: '支付有礼',
          routerLink: ['/extensions/ecommerce/pay-gift-list'],
          requiresPermission: 'ReadPaymentRewardActivity',
        },
        {
          id: 'exclusion-group-list',
          label: '低价限购',
          routerLink: ['/extensions/ecommerce/exclusion-group-list'],
          requiresPermission: 'ReadExclusionGroup',
        },
        {
          id: 'shopping-credits-grant-list',
          label: '购物金发放活动',
          routerLink: ['/extensions/ecommerce/shopping-credits-grant-list'],
          requiresPermission: 'ReadShoppingCreditsClaimActivity',
        },
        {
          id: 'shopping-credits-use-list',
          label: '购物金抵扣活动',
          routerLink: ['/extensions/ecommerce/shopping-credits-use-list'],
          requiresPermission: 'ReadShoppingCreditsDeductionActivity',
        },
        {
          id: 'activity-countdown-list',
          label: '活动倒计时管理',
          routerLink: ['/extensions/ecommerce/activity-countdown-list'],
          icon: 'scroll',
          requiresPermission: 'ReadActivityCountdown',
        },
      ],
    }),
    addNavMenuItem(
      {
        id: 'floatWindowConfig',
        label: '浮窗设置',
        routerLink: ['/extensions/ecommerce/float-window-config'],
        icon: 'user',
        requiresPermission: 'ReadFloatingWindow',
      },
      'settings',
    ),
    addNavMenuItem(
      {
        id: 'genralSetting',
        label: '通用设置',
        routerLink: ['/extensions/ecommerce/general-setting'],
        icon: 'user',
        requiresPermission: userPermissions => {
          return ['CreateWeChatConfig', 'CreateGatherWaterPondConfig', 'UpdateQiYuConfig', 'UMengConfig'].some(item =>
            userPermissions.includes(item),
          );
        },
      },
      'settings',
    ),
    addNavMenuItem(
      {
        id: 'channels',
        label: '销售渠道',
        routerLink: ['/extensions/ecommerce/channel-list'],
        icon: 'layers',
        requiresPermission: 'ReadChannel',
      },
      'settings',
    ),
    addNavMenuSection(
      {
        id: 'dataStatistics',
        label: '数据统计',
        requiresPermission: 'Statistics',
        items: [
          {
            id: 'pageStatistics',
            label: '页面统计',
            routerLink: ['/extensions/ecommerce/page-statistics'],
            icon: 'calendar',
          },
          {
            id: 'dataBoard',
            label: '数据看板',
            routerLink: ['/extensions/ecommerce/data-board'],
            icon: 'data-cluster',
          },
          {
            id: 'productInsights',
            label: '商品洞察',
            routerLink: ['/extensions/ecommerce/product-insights'],
            icon: 'eye',
          },
          {
            id: 'customerTrade',
            label: '客户交易',
            routerLink: ['/extensions/ecommerce/customer-trade'],
            icon: 'id-badge',
          },
        ],
      },
      'catalog',
    ),
  ],
})
export class EcommerceCommonUISharedModule {
  // constructor(private styleService: StyleService) {}
  ngOnInit() {
    // this.styleService.addStyles(['path/to/plugin-styles.css']);
  }
}
