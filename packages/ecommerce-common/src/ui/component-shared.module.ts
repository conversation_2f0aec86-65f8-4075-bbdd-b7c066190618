import {NgModule} from '@angular/core';

import {SharedModule} from '@vendure/admin-ui/core';
import {AppFormFieldModule} from './components/forms/app-form-field/app-form-field.module';
import {OptionValueInputComponent} from './components/option-value-input/option-value-input.component';
import {AppRadioComponent} from './components/forms/app-radio/app-radio.component';
import {AppSelectAsyncComponent} from './components/forms/app-select-async/app-select-async.component';
import {DateRangeModule} from './components/date-range/date-range.module';
import {AppSelectProductComponent} from './components/forms/app-select-product/app-select-product.component';
import {AppPriceInputComponent} from './components/forms/app-price-input/app-price-input.component';
import {SelectMemberCardModule} from './components/select-member-card/select-member-card.module';
import {AppCheckboxComponent} from './components/forms/app-checkbox/app-checkbox.component';
import {AppButtonDeleteComponent} from './components/forms/app-button-delete/app-button-delete.component';
import {AppJumpComponent} from './components/forms/app-jump/app-jump.component';
import {AppFailDelBtnComponent} from './components/forms/app-fail-del-btn/app-fail-del-btn.component';
import {AppDatePickerModule} from './components/forms/app-date-picker/app-date-picker.module';
import {LabelValueModule} from './components/label-value/label-value.module';
import {ConditionTooltipModule} from './components/base/condition-tooltip/condition-tooltip.module';
import {PriceInputComponent} from './components/forms/price-input/price-input.component';
import {ItemsPerPageControlsComponent} from './components/base/items-per-page-controls/items-per-page-controls.component';
import {VirtualProductLabelModule} from './components/base/virtual-product-label/virtual-product-label.module';
import {AppJumpExternalComponent} from './components/forms/app-jump-external/app-jump-external.component';
import {AppDateRangerModule} from './components/forms/app-date-ranger/app-date-ranger.module';
import {AppTimePickerComponent} from './components/forms/app-time-picker/app-time-picker.component';

import {IntegerDirective} from './directives/input/integer.directive';
import {DataTableComponent} from './components/base/data-table/data-table.component';
import {ImageFileInputModule} from './components/forms/image-file-input/image-file-input.module';
import {AppCustomerLimitComponent} from './components/forms/app-customer-limit/app-customer-limit.component';

// import {ChipComponent} from './components/chip/chip.component';
const COMPS = [
  PriceInputComponent,
  OptionValueInputComponent,
  AppRadioComponent,
  AppSelectAsyncComponent,
  AppPriceInputComponent,
  AppCheckboxComponent,
  AppButtonDeleteComponent,
  AppJumpComponent,
  AppJumpExternalComponent,
  AppFailDelBtnComponent,
  AppSelectProductComponent,
  ItemsPerPageControlsComponent,
  AppTimePickerComponent,
  DataTableComponent,
  AppCustomerLimitComponent,
];
@NgModule({
  imports: [
    SharedModule,
    AppFormFieldModule,
    DateRangeModule,
    VirtualProductLabelModule,
    ImageFileInputModule,
    SelectMemberCardModule,
    LabelValueModule,
  ],
  declarations: [...COMPS, IntegerDirective],
  exports: [
    SharedModule,
    AppFormFieldModule,
    ...COMPS,
    DateRangeModule,
    SelectMemberCardModule,
    AppDatePickerModule,
    AppDateRangerModule,
    LabelValueModule,
    ConditionTooltipModule,
    VirtualProductLabelModule,
    IntegerDirective,
    ImageFileInputModule,
  ],
})
export class ComponentSharedModule {}
