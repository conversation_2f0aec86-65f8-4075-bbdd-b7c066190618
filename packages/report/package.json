{"name": "@scmally/report", "description": "报表模板功能模块", "version": "0.1.0", "keywords": ["report", "template", "dynamic-sql"], "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=16"}, "author": "", "license": "UNLICENSED", "publishConfig": {"access": "public"}, "scripts": {"build": "nb-tsc", "clean": "del 'report*.tgz' 'dist/*' '*.tsbuildinfo' 'package'", "pretest": "yarn run clean && yarn run build", "test": "yarn run pretest && yarn run jest", "jest": "jest", "gen": "graphql-codegen --config ./codegen.json"}, "dependencies": {"@scmally/ecommerce-common": "^0.1.0", "@scmally/kvs": "^0.1.0", "@vendure/core": "^2.0.0"}, "devDependencies": {"@graphql-codegen/add": "2.0.2", "@graphql-codegen/cli": "1.21.1", "@graphql-codegen/typescript": "1.21.0", "@graphql-codegen/typescript-compatibility": "2.0.1", "@graphql-codegen/typescript-operations": "1.17.14", "@nutol/build": "^0.2.13", "@nutol/eslint-config": "^0.2.8", "@types/jest": "^29.4.0", "@types/node": "^18.13.0", "del-cli": "^5.0.0", "jest": "^29.4.2"}, "files": ["README.md", "dist", "src", "!*/__tests__"]}