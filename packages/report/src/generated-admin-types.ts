// tslint:disable
export type Maybe<T> = T | null;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  /** A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format. */
  DateTime: any;
  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */
  JSON: any;
};

export type ReportTemplate = Node & {
  __typename?: 'ReportTemplate';
  id: Scalars['ID'];
  templateName: Scalars['String'];
  filterConfig: Scalars['String'];
  headerConfig: Scalars['String'];
  sqlStatement: Scalars['String'];
  createdBy: Scalars['ID'];
  updatedBy?: Maybe<Scalars['ID']>;
  createdAt: Scalars['DateTime'];
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export type ReportTemplateList = PaginatedList & {
  __typename?: 'ReportTemplateList';
  items: Array<ReportTemplate>;
  totalItems: Scalars['Int'];
};

export type ReportQueryResult = {
  __typename?: 'ReportQueryResult';
  headers: Array<HeaderMeta>;
  rows: Array<Scalars['JSON']>;
  stats: ExecutionStats;
};

export type HeaderMeta = {
  __typename?: 'HeaderMeta';
  field: Scalars['String'];
  name: Scalars['String'];
};

export type ExecutionStats = {
  __typename?: 'ExecutionStats';
  executionTime: Scalars['Int'];
  totalRows: Scalars['Int'];
};

export enum ReportErrorCode {
  SqlInjectionRisk = 'SQL_INJECTION_RISK',
  HeaderMismatch = 'HEADER_MISMATCH',
  Timeout = 'TIMEOUT',
  InvalidFilter = 'INVALID_FILTER'
}

export type ReportPagination = {
  page: Scalars['Int'];
  pageSize: Scalars['Int'];
};

export type ReportFilterInput = {
  field: Scalars['String'];
  operator: Scalars['String'];
  value?: Maybe<Scalars['JSON']>;
};

export type ReportQueryInput = {
  templateId: Scalars['ID'];
  filters?: Maybe<Array<ReportFilterInput>>;
  pagination?: Maybe<ReportPagination>;
};

export type ReportTemplateInput = {
  id?: Maybe<Scalars['ID']>;
  templateName: Scalars['String'];
  filterConfig: Scalars['String'];
  headerConfig: Scalars['String'];
  sqlStatement: Scalars['String'];
};

export type ReportTemplateListOptions = {
  /** Skips the first n results, for use in pagination */
  skip?: Maybe<Scalars['Int']>;
  /** Takes n results, for use in pagination */
  take?: Maybe<Scalars['Int']>;
  /** Specifies which properties to sort the results by */
  sort?: Maybe<ReportTemplateSortParameter>;
  /** Allows the results to be filtered */
  filter?: Maybe<ReportTemplateFilterParameter>;
  /** Specifies whether multiple "filter" arguments should be combines with a logical AND or OR operation. Defaults to AND. */
  filterOperator?: Maybe<LogicalOperator>;
};

export type ReportTemplateSortParameter = {
  id?: Maybe<SortOrder>;
  templateName?: Maybe<SortOrder>;
  createdAt?: Maybe<SortOrder>;
  updatedAt?: Maybe<SortOrder>;
};

export type ReportTemplateFilterParameter = {
  id?: Maybe<IdOperators>;
  templateName?: Maybe<StringOperators>;
  createdAt?: Maybe<DateOperators>;
  updatedAt?: Maybe<DateOperators>;
};

export type Node = {
  id: Scalars['ID'];
};

export type PaginatedList = {
  items: Array<Node>;
  totalItems: Scalars['Int'];
};

export enum LogicalOperator {
  And = 'AND',
  Or = 'OR'
}

export enum SortOrder {
  Asc = 'ASC',
  Desc = 'DESC'
}

export type IdOperators = {
  eq?: Maybe<Scalars['String']>;
  notEq?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Scalars['String']>>;
  notIn?: Maybe<Array<Scalars['String']>>;
  isNull?: Maybe<Scalars['Boolean']>;
};

export type StringOperators = {
  eq?: Maybe<Scalars['String']>;
  notEq?: Maybe<Scalars['String']>;
  contains?: Maybe<Scalars['String']>;
  notContains?: Maybe<Scalars['String']>;
  in?: Maybe<Array<Scalars['String']>>;
  notIn?: Maybe<Array<Scalars['String']>>;
  regex?: Maybe<Scalars['String']>;
  isNull?: Maybe<Scalars['Boolean']>;
};

export type DateOperators = {
  eq?: Maybe<Scalars['DateTime']>;
  before?: Maybe<Scalars['DateTime']>;
  after?: Maybe<Scalars['DateTime']>;
  between?: Maybe<DateRange>;
  isNull?: Maybe<Scalars['Boolean']>;
};

export type DateRange = {
  start: Scalars['DateTime'];
  end: Scalars['DateTime'];
};
